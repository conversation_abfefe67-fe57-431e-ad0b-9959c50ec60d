using Prcsrly.Fly.Framework.Shared.Common;
using Prcsrly.Fly.Framework.Shared.Common.Requests;
using Prcsrly.Fly.Shared.Common;

namespace Prcsrly.Fly.Shared.Modules.Store.Requests;

public class CreateDisbursementPermitRequest : CreateUpdateRequestBase
{
    public Identifier CostCenterId { get; set; }
    public Identifier StoreId { get; set; }
    public DateTime? Date { get; set; }

    public IEnumerable<CreateUpdateDisbursementPermitDetailRequest>? DisbursementPermitDetails { get; set; }
}

public class UpdateDisbursementPermitRequest : UpdateRequestBase
{
    public Identifier CostCenterId { get; set; }
    public Identifier StoreId { get; set; }
    public DateTime? Date { get; set; }

    public IEnumerable<CreateUpdateDisbursementPermitDetailRequest>? DisbursementPermitDetails { get; set; }
}