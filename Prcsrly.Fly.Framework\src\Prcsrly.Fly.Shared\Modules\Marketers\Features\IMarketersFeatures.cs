using Prcsrly.Fly.Framework.Shared.Common.Features;
using Prcsrly.Fly.Framework.Shared.Common.Requests;
using Prcsrly.Fly.Shared.Modules.Accounting.Dtos;
using Prcsrly.Fly.Shared.Modules.Accounting.Requests;
using Prcsrly.Fly.Shared.Modules.Marketers.Dtos;
using Prcsrly.Fly.Shared.Modules.Marketers.Requests;
using Prcsrly.Fly.Shared.Modules.PurchaseAndSales.Dtos;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Prcsrly.Fly.Shared.Modules.Marketers.Features
{
    public interface IMarketersFeatures
    : IFeatureBase<DtoMarketer, MarketerQueryRequest, MarketerDeleteRequest, MarketerCreateUpdateRequest>
    {
        
    }

    public interface IMarketerDisbursementPermitFeatures
    : IFeatureBase<DtoMarketerDisbursementPermit, MarketerDisbursementPermitQueryRequest, MarketerDisbursementPermitDeleteRequestBase, MarketerDisbursementPermitCreateUpdateRequest>
    {
        
    }

    public interface IMarketerSalesInvoiceFeatures
    : IFeatureBase<DtoMarketerSalesInvoice, MarketerSalesInvoiceQueryRequest, MarketerDeleteSalesInvoiceRequest, MarketerSalesInvoiceCreateRequest>
    {
        
    }
}
