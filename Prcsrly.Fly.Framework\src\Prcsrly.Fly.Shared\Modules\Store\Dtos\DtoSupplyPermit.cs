using Prcsrly.Fly.Framework.Shared.Common;
using Prcsrly.Fly.Framework.Shared.Common.Attributes;
using Prcsrly.Fly.Framework.Shared.Common.Dtos;
using System.Text.Json.Serialization;

namespace Prcsrly.Fly.Shared.Modules.Store.Dtos;

public class DtoSupplyPermit : DtoBase
{
    [ClientInvisible]
    public Identifier SupplierId { get; set; }
    public string SupplierName { get; set; }
    [ClientInvisible]
    public Identifier StoreId { get; set; }
    public string StoreName { get; set; }
    [ClientInvisible]
    public DateTime Date { get; set; }

    public string TimwFormated => Date.ToString("HH:mm") ?? "";
    [JsonIgnore]
    public string DateFormated => Date.ToString("yyyy.MM.dd") ?? "";
    [ClientInvisible]
    public ICollection<DtoSupplyPermitDetail>? SupplyPermitDetails { get; set; }

    public string DetailsString
    {
        get
        {
            var def = "";


            if (SupplyPermitDetails?.Any() == true)
            {
                var tt = new List<string>();

                foreach (var item in SupplyPermitDetails)
                {
                    tt.Add($"[{item.Quantity} {item.UnitName} {item.ItemName}]");
                }
                def = string.Join(", ", tt);
            }


            return def;
        }
    }
}

public class DtoSupplyPermitDetail : DtoBase
{
    [ClientInvisible]
    public Identifier SupplyPermitId { get; set; }
    [ClientInvisible]
    public Identifier ItemId { get; set; }
    [ClientInvisible]
    public Identifier UnitId { get; set; }

    public string? UnitName { get; set; }
    public string? ItemName { get; set; }

    public decimal Quantity { get; set; }
    public DateTime ExpireDate { get; set; }
}