using Prcsrly.Fly.Framework.Shared.Common.Features;
using Prcsrly.Fly.Framework.Shared.Common.Requests;
using Prcsrly.Fly.Shared.Modules.Accounting.Dtos;
using Prcsrly.Fly.Shared.Modules.Accounting.Requests;

namespace Prcsrly.Fly.Shared.Modules.Accounting.Features;

public interface ICurrencyFeatures : IFeatureBase<DtoCurrency, CurrencyQueryRequest, DeleteRequestBase, CreateUpdateCurrencyRequest>
{
}