using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Prcsrly.Fly.Framework.Server.Common
{
    public class CustomLoggingService
    {
        private readonly ILogger _logger;

        public CustomLoggingService(ILogger logger)
        {
            _logger = logger;
        }

        public void TraceGettingServices(params Type[] types)
        {
            var names = types.Any() ? string.Join(", ", types.Select(s => s.Name)) : "";
            _logger.LogTrace("Getting services [{0}]", names);
        }
    }
}
