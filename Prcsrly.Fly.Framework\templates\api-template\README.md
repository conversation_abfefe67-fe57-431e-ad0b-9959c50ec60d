# FlyApiTemplate

A modern ASP.NET Core Web API template built with the Prcsrly.Fly.Framework.

## Features

- ✅ **Fly Framework Integration** - Built on top of Prcsrly.Fly.Framework for rapid development
- ✅ **CQRS Pattern** - Command Query Responsibility Segregation with MediatR
- ✅ **Entity Framework Core** - Database access with multiple provider support
- ✅ **AutoMapper & Mapster** - Object-to-object mapping
- ✅ **Swagger/OpenAPI** - API documentation and testing
- ✅ **Structured Logging** - Serilog integration with file and console output
- ✅ **Authentication Ready** - JWT authentication support (optional)
- ✅ **Sample CRUD Operations** - Complete Product entity with full CRUD operations

## Getting Started

### Prerequisites

- .NET 8.0 SDK or later
- SQL Server, MySQL, or PostgreSQL (depending on your choice)

### Installation

1. **Create a new project from this template:**
   ```bash
   dotnet new install Prcsrly.Fly.Framework.ApiTemplate
   dotnet new flyapi -n MyApiProject
   cd MyApiProject
   ```

2. **Update the connection string** in `appsettings.json`:
   ```json
   {
     "ConnectionStrings": {
       "DefaultConnection": "your-connection-string-here"
     }
   }
   ```

3. **Run the application:**
   ```bash
   dotnet run
   ```

4. **Access the API:**
   - Swagger UI: `https://localhost:5001` (or your configured port)
   - API Base URL: `https://localhost:5001/api`

## Project Structure

```
FlyApiTemplate/
├── Controllers/          # API Controllers
├── Data/                # Database Context
├── Dtos/                # Data Transfer Objects
├── Entities/            # Domain Entities
├── Handlers/            # MediatR Request Handlers
├── Mapping/             # AutoMapper & Mapster Configurations
├── Requests/            # CQRS Commands and Queries
├── Services/            # Business Logic Services
├── Program.cs           # Application Entry Point
└── appsettings.json     # Configuration
```

## Sample API Endpoints

The template includes a complete Product entity with the following endpoints:

- `GET /api/products` - Get all products with filtering
- `GET /api/products/{id}` - Get product by ID
- `POST /api/products` - Create new product
- `PUT /api/products/{id}` - Update product
- `DELETE /api/products/{id}` - Delete product
- `GET /api/products/category/{category}` - Get products by category
- `GET /api/products/price-range?minPrice=10&maxPrice=100` - Get products by price range
- `GET /api/products/sku/{sku}` - Get product by SKU

## Configuration

### Database Providers

The template supports multiple database providers. Update your connection string and ensure the correct provider is configured in the project file.

### Authentication

If you enabled authentication during template creation, configure the JWT settings in `appsettings.json`:

```json
{
  "Authentication": {
    "JwtSettings": {
      "SecretKey": "your-secret-key-here",
      "Issuer": "YourApp",
      "Audience": "YourApp-Users",
      "ExpirationInMinutes": 60
    }
  }
}
```

### Logging

Logs are written to both console and files. Configure logging levels in `appsettings.json` under the `Serilog` section.

## Development

### Adding New Entities

1. Create the entity in `Entities/`
2. Create the DTO in `Dtos/`
3. Create requests in `Requests/`
4. Create service interface and implementation in `Services/`
5. Create MediatR handlers in `Handlers/`
6. Create controller in `Controllers/`
7. Add mapping configurations in `Mapping/`
8. Update `ApplicationDbContext` to include the new entity

### Database Migrations

```bash
# Add migration
dotnet ef migrations add InitialCreate

# Update database
dotnet ef database update
```

## Built With

- [ASP.NET Core 8.0](https://docs.microsoft.com/en-us/aspnet/core/)
- [Prcsrly.Fly.Framework](https://github.com/prcsrly/Fly-Framework)
- [Entity Framework Core](https://docs.microsoft.com/en-us/ef/core/)
- [MediatR](https://github.com/jbogard/MediatR)
- [AutoMapper](https://automapper.org/)
- [Mapster](https://github.com/MapsterMapper/Mapster)
- [Serilog](https://serilog.net/)
- [Swagger/OpenAPI](https://swagger.io/)

## License

This template is licensed under the MIT License.
