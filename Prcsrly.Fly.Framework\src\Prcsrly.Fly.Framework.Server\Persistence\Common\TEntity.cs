using Prcsrly.Fly.Framework.Server.Persistence.ValueGenerators;
using Prcsrly.Fly.Framework.Shared.Common;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Microsoft.EntityFrameworkCore.ValueGeneration;
using Newtonsoft.Json;
using System.Text.Json;
using System.Xml;

namespace Prcsrly.Fly.Framework.Server.Persistence.Common;



public class TEntity
{
    public Guid Guid { get; set; }
    public DateTime CreateDate { get; set; }

    //public int PartId { get; set; }

    public Identifier Id { get; set; }
    public bool IsDeleted { get; set; }
    public Identifier TenantId { get; set; }

    public virtual string Display() => "";
}

public enum SyncObjectType
{
    None = 'N',
    Query = 'Q',
    Object = 'O',
}
public enum SyncType
{
    None = 'N',
    Create = 'C',
    Update = 'U',
    Delete = 'D'
}
public enum SyncMode
{
    None = 'N',
    Idle = 'I',
    Process = 'P',
    Stop = 'S',
    Complete = 'C',
}

public class TSync : TEntity
{
    public Identifier TargetId { get; set; }
    public string TargetDisplay { get; set; } = string.Empty;
    public string TypeFullName { get; set; } = string.Empty;
    public string JsonData { get; set; } = string.Empty;
    public SyncType SyncType { get; set; } = SyncType.None;
    public SyncMode SyncMode { get; set; } = SyncMode.None;
    public SyncObjectType SyncObjectType { get; set; } = SyncObjectType.None;
    public long FingerPrint { get; set; }
    public long PartId { get; set; }


    public DateTime FirstCreate { get; set; }
    public bool IsSync { get; set; }

    public static TSync Generate(DateTime d, int currentDeviceInfo, string entTypeFullName, EntityState changedEntryState, TEntity baseEntity)
    {
        var xid = d.ToString("yyMMddHHmmssfffffff");
        var longXid = long.Parse(xid);

        return new TSync
        {

            CreateDate = d,
            FingerPrint = longXid,
            FirstCreate = d,
            IsSync = false,
            PartId = currentDeviceInfo,
            TypeFullName = entTypeFullName,
            SyncType = changedEntryState switch
            {
                EntityState.Added => SyncType.Create,
                EntityState.Modified => SyncType.Update,
                EntityState.Deleted => SyncType.Delete,
                _ => SyncType.None,
            },
            SyncMode = SyncMode.None,
            TargetId = baseEntity.Id,
            TargetDisplay = baseEntity.Display(),
            SyncObjectType = SyncObjectType.Object,
            JsonData = JsonConvert.SerializeObject(baseEntity, new JsonSerializerSettings()
            {
                ReferenceLoopHandling = ReferenceLoopHandling.Ignore,
                MaxDepth = 1,
               // Converters = new JsonConverter[] { new IdentifierConverter() }
            }),
        };
    }
    public static TSync GenerateQuery(DateTime d, int currentDeviceInfo, string entTypeFullName, EntityState changedEntryState, TEntity? baseEntity, string q)
    {
        var xid = d.ToString("yyMMddHHmmssfffffff");
        var longXid = long.Parse(xid);

        return new TSync
        {

            CreateDate = d,
            FingerPrint = longXid,
            FirstCreate = d,
            IsSync = false,
            PartId = currentDeviceInfo,
            TypeFullName = entTypeFullName,
            SyncType = changedEntryState switch
            {
                EntityState.Added => SyncType.Create,
                EntityState.Modified => SyncType.Update,
                EntityState.Deleted => SyncType.Delete,
                _ => SyncType.None,
            },
            SyncMode = SyncMode.None,
            TargetId = baseEntity?.Id ?? Identifier.Empty,
            TargetDisplay = baseEntity?.Display() ?? "",
            SyncObjectType = SyncObjectType.Query,
            JsonData = q,
        };
    }

    internal sealed class EntityConfiguration : IEntityTypeConfiguration<TSync>
    {

        private void Configurx(EntityTypeBuilder<TSync> builder)
        {
            builder.HasKey(e => e.Id);
            builder.HasIndex(x => new { x.FingerPrint, x.PartId, x.IsSync});

            builder
                .Property(x => x.Id)
                .ValueGeneratedOnAdd()
                //.UseMySqlIdentityColumn()
                //.HasConversion<IdentifierConverter>()
                ;

            builder
                .Property(x => x.Guid)
                .HasValueGenerator<GuidValueGenerator>()
                .ValueGeneratedOnAdd();

            builder
                .Property(x => x.CreateDate)
                .HasValueGenerator<MyDateGenerator>()
                .ValueGeneratedOnAdd();


            //builder
            //    .Property(x => x.UpdateDate)
            //    .HasValueGenerator<MyDateGenerator>()
            //    .ValueGeneratedOnUpdate()
            //;

            builder.HasQueryFilter(x => !x.IsDeleted);
        }

        public void Configure(EntityTypeBuilder<TSync> builder)
        {
            Configurx(builder);
        }
    }

}
