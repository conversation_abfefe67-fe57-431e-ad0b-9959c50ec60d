using FlyApiTemplate.Dtos;
using FlyApiTemplate.Entities;
using Microsoft.Extensions.Logging;
using Prcsrly.Fly.Framework.Server.Persistence.Implementations;
using AutoMapper;
using MapsterMapper;

namespace FlyApiTemplate.Services;

public class ProductService : ServiceBase<ProductDto, Product>, IProductService
{
    public ProductService(
        ILogger<ProductService> logger,
        AutoMapper.IMapper mapper,
        IMapper mapsterMapper,
        IServiceProvider serviceProvider)
        : base(logger, mapper, mapsterMapper, serviceProvider)
    {
    }

    public async ValueTask<IEnumerable<ProductDto>> GetProductsByCategoryAsync(string category)
    {
        var products = await GetAllAsync(p => p.Category == category && p.IsActive);
        return products;
    }

    public async ValueTask<IEnumerable<ProductDto>> GetProductsByPriceRangeAsync(decimal minPrice, decimal maxPrice)
    {
        var products = await GetAllAsync(p => p.Price >= minPrice && p.Price <= maxPrice && p.IsActive);
        return products;
    }

    public async ValueTask<ProductDto?> GetProductBySkuAsync(string sku)
    {
        var products = await GetAllAsync(p => p.SKU == sku);
        return products.FirstOrDefault();
    }

    public async ValueTask<bool> IsSkuUniqueAsync(string sku, Guid? excludeProductId = null)
    {
        var query = Repositry.GetAll(p => p.SKU == sku);
        
        if (excludeProductId.HasValue)
        {
            query = query.Where(p => p.Guid != excludeProductId.Value);
        }
        
        return !query.Any();
    }

    public override async ValueTask<bool> BeforeAdd(Product entity)
    {
        // Set creation timestamp
        entity.LastUpdated = DateTime.UtcNow;
        
        // Generate SKU if not provided
        if (string.IsNullOrEmpty(entity.SKU))
        {
            entity.SKU = GenerateUniqueSku(entity.Name);
        }
        
        // Validate SKU uniqueness
        var isUnique = await IsSkuUniqueAsync(entity.SKU);
        if (!isUnique)
        {
            throw new InvalidOperationException($"SKU '{entity.SKU}' already exists.");
        }
        
        return await base.BeforeAdd(entity);
    }

    public override async ValueTask<bool> BeforeUpdate(Product entity)
    {
        // Update timestamp
        entity.LastUpdated = DateTime.UtcNow;
        
        // Validate SKU uniqueness (excluding current product)
        if (!string.IsNullOrEmpty(entity.SKU))
        {
            var isUnique = await IsSkuUniqueAsync(entity.SKU, entity.Guid);
            if (!isUnique)
            {
                throw new InvalidOperationException($"SKU '{entity.SKU}' already exists.");
            }
        }
        
        return await base.BeforeUpdate(entity);
    }

    private string GenerateUniqueSku(string productName)
    {
        // Simple SKU generation logic - you can customize this
        var prefix = productName.Length >= 3 ? productName.Substring(0, 3).ToUpper() : productName.ToUpper();
        var timestamp = DateTime.UtcNow.ToString("yyyyMMddHHmmss");
        return $"{prefix}-{timestamp}";
    }
}
