{"format": 1, "restore": {"C:\\Users\\<USER>\\source\\repos\\vscode\\prcsrly\\SmartiERP\\SmartiERP-0.0.2-fullstack\\frontend\\SmartiErpWin.Client\\SmartiErpWin.Client.csproj": {}}, "projects": {"C:\\Users\\<USER>\\source\\repos\\vscode\\prcsrly\\SmartiERP\\SmartiERP-0.0.2-fullstack\\freamework\\Fly.Framework.Shared\\Fly.Framework.Shared.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\source\\repos\\vscode\\prcsrly\\SmartiERP\\SmartiERP-0.0.2-fullstack\\freamework\\Fly.Framework.Shared\\Fly.Framework.Shared.csproj", "projectName": "Fly.Framework.Shared", "projectPath": "C:\\Users\\<USER>\\source\\repos\\vscode\\prcsrly\\SmartiERP\\SmartiERP-0.0.2-fullstack\\freamework\\Fly.Framework.Shared\\Fly.Framework.Shared.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\source\\repos\\vscode\\prcsrly\\SmartiERP\\SmartiERP-0.0.2-fullstack\\freamework\\Fly.Framework.Shared\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net8.0"], "sources": {"E:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.DependencyInjection.Abstractions": {"target": "Package", "version": "[8.0.1, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "E:\\Program Files\\dotnet\\sdk\\9.0.201/PortableRuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\source\\repos\\vscode\\prcsrly\\SmartiERP\\SmartiERP-0.0.2-fullstack\\frontend\\SmartiErpWin.Client\\SmartiErpWin.Client.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\source\\repos\\vscode\\prcsrly\\SmartiERP\\SmartiERP-0.0.2-fullstack\\frontend\\SmartiErpWin.Client\\SmartiErpWin.Client.csproj", "projectName": "SmartiErpWin.Client", "projectPath": "C:\\Users\\<USER>\\source\\repos\\vscode\\prcsrly\\SmartiERP\\SmartiERP-0.0.2-fullstack\\frontend\\SmartiErpWin.Client\\SmartiErpWin.Client.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\source\\repos\\vscode\\prcsrly\\SmartiERP\\SmartiERP-0.0.2-fullstack\\frontend\\SmartiErpWin.Client\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net8.0-windows10.0.22621.0"], "sources": {"E:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0-windows10.0.22621": {"targetAlias": "net8.0-windows10.0.22621.0", "projectReferences": {"C:\\Users\\<USER>\\source\\repos\\vscode\\prcsrly\\SmartiERP\\SmartiERP-0.0.2-fullstack\\freamework\\Fly.Framework.Shared\\Fly.Framework.Shared.csproj": {"projectPath": "C:\\Users\\<USER>\\source\\repos\\vscode\\prcsrly\\SmartiERP\\SmartiERP-0.0.2-fullstack\\freamework\\Fly.Framework.Shared\\Fly.Framework.Shared.csproj"}, "C:\\Users\\<USER>\\source\\repos\\vscode\\prcsrly\\SmartiERP\\SmartiERP-0.0.2-fullstack\\frontend\\SmartiErpWin.Core\\SmartiErpWin.Core.csproj": {"projectPath": "C:\\Users\\<USER>\\source\\repos\\vscode\\prcsrly\\SmartiERP\\SmartiERP-0.0.2-fullstack\\frontend\\SmartiErpWin.Core\\SmartiErpWin.Core.csproj"}, "C:\\Users\\<USER>\\source\\repos\\vscode\\prcsrly\\SmartiERP\\SmartiERP-0.0.2-fullstack\\shared\\Fly.Shared\\Fly.Shared.csproj": {"projectPath": "C:\\Users\\<USER>\\source\\repos\\vscode\\prcsrly\\SmartiERP\\SmartiERP-0.0.2-fullstack\\shared\\Fly.Shared\\Fly.Shared.csproj"}, "C:\\Users\\<USER>\\source\\repos\\vscode\\prcsrly\\SmartiERP\\SmartiERP-0.0.2-fullstack\\src\\tools\\Fly.Framework.Desktop\\Fly.Framework.Desktop.csproj": {"projectPath": "C:\\Users\\<USER>\\source\\repos\\vscode\\prcsrly\\SmartiERP\\SmartiERP-0.0.2-fullstack\\src\\tools\\Fly.Framework.Desktop\\Fly.Framework.Desktop.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net8.0-windows10.0.22621": {"targetAlias": "net8.0-windows10.0.22621.0", "dependencies": {"AutoMapper": {"target": "Package", "version": "[13.0.1, )"}, "ClosedXML": {"target": "Package", "version": "[0.102.2, )"}, "CommunityToolkit.Mvvm": {"target": "Package", "version": "[8.2.2, )"}, "Humanizer.Core": {"target": "Package", "version": "[2.14.1, )"}, "Humanizer.Core.ar": {"target": "Package", "version": "[2.14.1, )"}, "MahApps.Metro": {"target": "Package", "version": "[2.4.10, )"}, "Mapster": {"target": "Package", "version": "[7.4.0, )"}, "Microsoft.AspNetCore.SignalR.Client": {"target": "Package", "version": "[8.0.4, )"}, "Microsoft.Extensions.Hosting": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Localization": {"target": "Package", "version": "[8.0.6, )"}, "ModernWpfUI": {"target": "Package", "version": "[0.9.6, )"}, "ReportViewerCore.NETCore": {"target": "Package", "version": "[15.1.22, )"}, "Serilog": {"target": "Package", "version": "[4.0.1, )"}, "Serilog.Sinks.Console": {"target": "Package", "version": "[6.0.0, )"}, "Serilog.Sinks.File": {"target": "Package", "version": "[6.0.0, )"}, "WPF-UI": {"target": "Package", "version": "[3.0.5, )"}, "WindowsCredentialManager": {"target": "Package", "version": "[0.1.0, )"}, "itext.bouncy-castle-adapter": {"target": "Package", "version": "[8.0.5, )"}, "itext7": {"target": "Package", "version": "[8.0.5, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.Windows.SDK.NET.Ref", "version": "[10.0.22621.57, 10.0.22621.57]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.Windows.SDK.NET.Ref.Windows": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WPF": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "E:\\Program Files\\dotnet\\sdk\\9.0.201/PortableRuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\source\\repos\\vscode\\prcsrly\\SmartiERP\\SmartiERP-0.0.2-fullstack\\frontend\\SmartiErpWin.Core\\SmartiErpWin.Core.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\source\\repos\\vscode\\prcsrly\\SmartiERP\\SmartiERP-0.0.2-fullstack\\frontend\\SmartiErpWin.Core\\SmartiErpWin.Core.csproj", "projectName": "SmartiErpWin.Core", "projectPath": "C:\\Users\\<USER>\\source\\repos\\vscode\\prcsrly\\SmartiERP\\SmartiERP-0.0.2-fullstack\\frontend\\SmartiErpWin.Core\\SmartiErpWin.Core.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\source\\repos\\vscode\\prcsrly\\SmartiERP\\SmartiERP-0.0.2-fullstack\\frontend\\SmartiErpWin.Core\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net8.0"], "sources": {"E:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"C:\\Users\\<USER>\\source\\repos\\vscode\\prcsrly\\SmartiERP\\SmartiERP-0.0.2-fullstack\\shared\\Fly.Shared\\Fly.Shared.csproj": {"projectPath": "C:\\Users\\<USER>\\source\\repos\\vscode\\prcsrly\\SmartiERP\\SmartiERP-0.0.2-fullstack\\shared\\Fly.Shared\\Fly.Shared.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"CommunityToolkit.Mvvm": {"target": "Package", "version": "[8.2.2, )"}, "FuzzySharp": {"target": "Package", "version": "[2.0.2, )"}, "Serilog": {"target": "Package", "version": "[4.0.1, )"}, "Serilog.Sinks.Console": {"target": "Package", "version": "[6.0.0, )"}, "Serilog.Sinks.File": {"target": "Package", "version": "[6.0.0, )"}, "Windows.Credentials.Manager": {"target": "Package", "version": "[1.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "E:\\Program Files\\dotnet\\sdk\\9.0.201/PortableRuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\source\\repos\\vscode\\prcsrly\\SmartiERP\\SmartiERP-0.0.2-fullstack\\shared\\Fly.Shared\\Fly.Shared.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\source\\repos\\vscode\\prcsrly\\SmartiERP\\SmartiERP-0.0.2-fullstack\\shared\\Fly.Shared\\Fly.Shared.csproj", "projectName": "Fly.Shared", "projectPath": "C:\\Users\\<USER>\\source\\repos\\vscode\\prcsrly\\SmartiERP\\SmartiERP-0.0.2-fullstack\\shared\\Fly.Shared\\Fly.Shared.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\source\\repos\\vscode\\prcsrly\\SmartiERP\\SmartiERP-0.0.2-fullstack\\shared\\Fly.Shared\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net8.0"], "sources": {"E:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"C:\\Users\\<USER>\\source\\repos\\vscode\\prcsrly\\SmartiERP\\SmartiERP-0.0.2-fullstack\\freamework\\Fly.Framework.Shared\\Fly.Framework.Shared.csproj": {"projectPath": "C:\\Users\\<USER>\\source\\repos\\vscode\\prcsrly\\SmartiERP\\SmartiERP-0.0.2-fullstack\\freamework\\Fly.Framework.Shared\\Fly.Framework.Shared.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"CommunityToolkit.Mvvm": {"target": "Package", "version": "[8.2.2, )"}, "FluentValidation.DependencyInjectionExtensions": {"target": "Package", "version": "[11.10.0, )"}, "MediatR": {"target": "Package", "version": "[12.4.1, )"}, "Microsoft.Extensions.Configuration.Abstractions": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.DependencyInjection.Abstractions": {"target": "Package", "version": "[8.0.1, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "E:\\Program Files\\dotnet\\sdk\\9.0.201/PortableRuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\source\\repos\\vscode\\prcsrly\\SmartiERP\\SmartiERP-0.0.2-fullstack\\src\\tools\\Fly.Framework.Desktop\\Fly.Framework.Desktop.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\source\\repos\\vscode\\prcsrly\\SmartiERP\\SmartiERP-0.0.2-fullstack\\src\\tools\\Fly.Framework.Desktop\\Fly.Framework.Desktop.csproj", "projectName": "Fly.Framework.Desktop", "projectPath": "C:\\Users\\<USER>\\source\\repos\\vscode\\prcsrly\\SmartiERP\\SmartiERP-0.0.2-fullstack\\src\\tools\\Fly.Framework.Desktop\\Fly.Framework.Desktop.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\source\\repos\\vscode\\prcsrly\\SmartiERP\\SmartiERP-0.0.2-fullstack\\src\\tools\\Fly.Framework.Desktop\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net8.0-windows"], "sources": {"E:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.DependencyInjection.Abstractions": {"target": "Package", "version": "[8.0.1, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}, "System.Management": {"target": "Package", "version": "[8.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WindowsForms": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "E:\\Program Files\\dotnet\\sdk\\9.0.201/PortableRuntimeIdentifierGraph.json"}}}}}