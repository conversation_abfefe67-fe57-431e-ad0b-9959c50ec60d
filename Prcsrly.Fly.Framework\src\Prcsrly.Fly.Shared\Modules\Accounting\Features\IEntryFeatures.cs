using Prcsrly.Fly.Framework.Shared.Common.Features;
using Prcsrly.Fly.Framework.Shared.Common.Http;
using Prcsrly.Fly.Framework.Shared.Common.Requests;
using Prcsrly.Fly.Framework.Shared.Common.ValueTypes;
using Prcsrly.Fly.Shared.Modules.Accounting.Dtos;
using Prcsrly.Fly.Shared.Modules.Accounting.Requests;
using Prcsrly.Fly.Shared.Modules.Accounting.Response;

namespace Prcsrly.Fly.Shared.Modules.Accounting.Features;

public interface IEntryFeatures : IFeatureBase<DtoEntry, EntryQueryRequest, EntryDeleteRequest, CreateEntryRequest>
{
    ValueTask<HttpResponseWrapper<long>> GetNextNumber();
    ValueTask<HttpResponseWrapper<DtoEntry>> PostCreateSimpleEntry(RSimpleRequest request);
    ValueTask<HttpResponseWrapperList<DefaultLookup>> GetLookupAccounts(AccountQueryRequest request);
    ValueTask<HttpResponseWrapperList<EntryReportResponse>> GetAccountMovmentReport(EntryReportRequest request);
}