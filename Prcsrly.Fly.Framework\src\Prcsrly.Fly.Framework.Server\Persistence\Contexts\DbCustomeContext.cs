using Prcsrly.Fly.Framework.Server.Persistence.Common;
using Prcsrly.Fly.Framework.Server.Persistence.ValueConverters;
using Prcsrly.Fly.Framework.Shared.Common;
using Prcsrly.Fly.Framework.Shared.Helpers;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Prcsrly.Fly.Framework.Server.Persistence.Contexts;

public abstract class DbCustomeContext : DbContext
{
    //  Infrastructure.MongoDB
    /**/
    public Guid ContextUniqeId { get; }
    /**/
    /**/
    protected DbCustomeContext()
    {
        ContextUniqeId = Guid.NewGuid();
        base.ChangeTracker.LazyLoadingEnabled = false;
        base.ChangeTracker.AutoDetectChangesEnabled = false;
        base.ChangeTracker.QueryTrackingBehavior = QueryTrackingBehavior.NoTracking;
    }
    protected DbCustomeContext(DbContextOptions options) : base(options)
    {
        ContextUniqeId = Guid.NewGuid();
        base.ChangeTracker.LazyLoadingEnabled = false;
        base.ChangeTracker.AutoDetectChangesEnabled = false;
        base.ChangeTracker.QueryTrackingBehavior = QueryTrackingBehavior.NoTracking;
    }
    /**/
    /**/
    //protected new virtual void OnModelCreating(ModelBuilder modelBuilder)
    //{
    //    modelBuilder.ApplyConfigurationsFromAssembly(typeof(DbCustomeContext).Assembly);
    //    base.OnModelCreating(modelBuilder);
    //}
    /**/
    /**/
    protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
    {
#if DEBUG
        optionsBuilder.EnableDetailedErrors();
        optionsBuilder.EnableSensitiveDataLogging();
#endif
        base.OnConfiguring(optionsBuilder);
    }
    /**/
    /**/
    protected override void ConfigureConventions(ModelConfigurationBuilder configurationBuilder)
    {
        configurationBuilder
            .Properties<Identifier>()
            .HaveConversion<IdentifierConverter>();

        configurationBuilder
            .Properties<decimal>()
            .HavePrecision(12, 2);
        base.ConfigureConventions(configurationBuilder);
    }
    /**/
    /**/
    public override int SaveChanges()
    {
        //  Log & Sync
        return base.SaveChanges();
    }

    private readonly List<TSync> _pendingSyncRecords = new();
    public override async Task<int> SaveChangesAsync(bool acceptAllChangesOnSuccess, CancellationToken cancellationToken = default)
    {
        var changed = ChangeTracker.Entries().Where(e => e.State == EntityState.Added ||
                                 e.State == EntityState.Modified ||
                                 e.State == EntityState.Deleted).ToList();
        var changes = await base.SaveChangesAsync(acceptAllChangesOnSuccess, cancellationToken);

        foreach (var changedEntry in changed)
        {
            if (changedEntry.Entity is TSync) continue;
            var ent = changedEntry.Entity;
            var baseEntity = ent as TEntity;
            //if (_pendingSyncRecords.Any(w => w.TargetId == baseEntity.Id)) continue;

            if (baseEntity is null) continue;

            var entType = changedEntry.Entity.GetType();
            //var entry = changedEntry.Entity ;
            //if()
            var _currentDeviceInfo = 0;
            var d = DatetimeHelpers.GetUTC();
            var xid = d.ToString("yyMMddHHmmssfffffff");
            var longXid = long.Parse(xid);
            var packet = TSync.Generate(d, _currentDeviceInfo, entType.FullName ?? "NONE", changedEntry.State, baseEntity);
            _pendingSyncRecords.Add(packet);
        }   // for
        if (_pendingSyncRecords.Any())
        {
            await base.AddRangeAsync(_pendingSyncRecords, cancellationToken);
            await base.SaveChangesAsync(acceptAllChangesOnSuccess, cancellationToken);
            _pendingSyncRecords.Clear();
        }

        return changes;
    }
    /**/
    /**/
}