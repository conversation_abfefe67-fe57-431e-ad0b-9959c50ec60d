{
  "$schema": "http://json.schemastore.org/launchsettings.json",
  "iisSettings": {
    "windowsAuthentication": false,
    "anonymousAuthentication": true,
    "iisExpress": {
      "applicationUrl": "http://localhost:5000",
      "sslPort": 5001
    }
  },
  "profiles": {
    "http": {
      "commandName": "Project",
      "dotnetRunMessages": true,
      "launchBrowser": true,
#if (UseSwagger)
      "launchUrl": "swagger",
#else
      "launchUrl": "api/products",
#endif
      "applicationUrl": "http://localhost:5000",
      "environmentVariables": {
        "ASPNETCORE_ENVIRONMENT": "Development"
      }
    },
#if (UseHttps)
    "https": {
      "commandName": "Project",
      "dotnetRunMessages": true,
      "launchBrowser": true,
#if (UseSwagger)
      "launchUrl": "swagger",
#else
      "launchUrl": "api/products",
#endif
      "applicationUrl": "https://localhost:5001;http://localhost:5000",
      "environmentVariables": {
        "ASPNETCORE_ENVIRONMENT": "Development"
      }
    },
#endif
    "IIS Express": {
      "commandName": "IISExpress",
      "launchBrowser": true,
#if (UseSwagger)
      "launchUrl": "swagger",
#else
      "launchUrl": "api/products",
#endif
      "environmentVariables": {
        "ASPNETCORE_ENVIRONMENT": "Development"
      }
    }
  }
}
