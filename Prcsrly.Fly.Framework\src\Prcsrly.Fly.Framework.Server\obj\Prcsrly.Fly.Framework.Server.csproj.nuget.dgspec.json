{"format": 1, "restore": {"C:\\Users\\<USER>\\source\\repos\\vscode\\prcsrly\\SmartiERP\\Prcsrly.Fly.Framework\\src\\Prcsrly.Fly.Framework.Server\\Prcsrly.Fly.Framework.Server.csproj": {}}, "projects": {"C:\\Users\\<USER>\\source\\repos\\vscode\\prcsrly\\SmartiERP\\Prcsrly.Fly.Framework\\src\\Prcsrly.Fly.Framework.Server\\Prcsrly.Fly.Framework.Server.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\source\\repos\\vscode\\prcsrly\\SmartiERP\\Prcsrly.Fly.Framework\\src\\Prcsrly.Fly.Framework.Server\\Prcsrly.Fly.Framework.Server.csproj", "projectName": "Prcsrly.Fly.Framework.Server", "projectPath": "C:\\Users\\<USER>\\source\\repos\\vscode\\prcsrly\\SmartiERP\\Prcsrly.Fly.Framework\\src\\Prcsrly.Fly.Framework.Server\\Prcsrly.Fly.Framework.Server.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\source\\repos\\vscode\\prcsrly\\SmartiERP\\Prcsrly.Fly.Framework\\src\\Prcsrly.Fly.Framework.Server\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\source\\repos\\vscode\\prcsrly\\SmartiERP\\Prcsrly.Fly.Framework\\NuGet.Config", "C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net8.0"], "sources": {"E:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}, "https://nuget.pkg.github.com/prcsrly/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"C:\\Users\\<USER>\\source\\repos\\vscode\\prcsrly\\SmartiERP\\Prcsrly.Fly.Framework\\src\\Prcsrly.Fly.Framework.Shared\\Prcsrly.Fly.Framework.Shared.csproj": {"projectPath": "C:\\Users\\<USER>\\source\\repos\\vscode\\prcsrly\\SmartiERP\\Prcsrly.Fly.Framework\\src\\Prcsrly.Fly.Framework.Shared\\Prcsrly.Fly.Framework.Shared.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Ardalis.Result.FluentValidation": {"target": "Package", "version": "[10.0.0, )"}, "AutoMapper": {"target": "Package", "version": "[13.0.1, )"}, "AutoMapper.Extensions.ExpressionMapping": {"target": "Package", "version": "[7.0.2, )"}, "FluentValidation.DependencyInjectionExtensions": {"target": "Package", "version": "[11.10.0, )"}, "LinqKit.Core": {"target": "Package", "version": "[1.2.7, )"}, "Mapster": {"target": "Package", "version": "[7.4.0, )"}, "MediatR": {"target": "Package", "version": "[12.4.1, )"}, "Microsoft.AspNetCore.Authentication.JwtBearer": {"target": "Package", "version": "[8.0.8, )"}, "Microsoft.AspNetCore.OpenApi": {"target": "Package", "version": "[8.0.8, )"}, "Microsoft.EntityFrameworkCore": {"target": "Package", "version": "[8.0.11, )"}, "Microsoft.EntityFrameworkCore.Relational": {"target": "Package", "version": "[8.0.11, )"}, "Microsoft.Extensions.Options.ConfigurationExtensions": {"target": "Package", "version": "[8.0.0, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}, "Pomelo.EntityFrameworkCore.MySql": {"target": "Package", "version": "[8.0.2, )"}, "Swashbuckle.AspNetCore": {"target": "Package", "version": "[6.7.3, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "E:\\Program Files\\dotnet\\sdk\\9.0.201/PortableRuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\source\\repos\\vscode\\prcsrly\\SmartiERP\\Prcsrly.Fly.Framework\\src\\Prcsrly.Fly.Framework.Shared\\Prcsrly.Fly.Framework.Shared.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\source\\repos\\vscode\\prcsrly\\SmartiERP\\Prcsrly.Fly.Framework\\src\\Prcsrly.Fly.Framework.Shared\\Prcsrly.Fly.Framework.Shared.csproj", "projectName": "Prcsrly.Fly.Framework.Shared", "projectPath": "C:\\Users\\<USER>\\source\\repos\\vscode\\prcsrly\\SmartiERP\\Prcsrly.Fly.Framework\\src\\Prcsrly.Fly.Framework.Shared\\Prcsrly.Fly.Framework.Shared.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\source\\repos\\vscode\\prcsrly\\SmartiERP\\Prcsrly.Fly.Framework\\src\\Prcsrly.Fly.Framework.Shared\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\source\\repos\\vscode\\prcsrly\\SmartiERP\\Prcsrly.Fly.Framework\\NuGet.Config", "C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net8.0"], "sources": {"E:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}, "https://nuget.pkg.github.com/prcsrly/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.DependencyInjection.Abstractions": {"target": "Package", "version": "[8.0.1, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "E:\\Program Files\\dotnet\\sdk\\9.0.201/PortableRuntimeIdentifierGraph.json"}}}}}