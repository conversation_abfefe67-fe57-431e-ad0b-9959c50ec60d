{"$schema": "http://json.schemastore.org/template", "author": "Prcsrly", "classifications": ["Web", "API", "Fly-Framework"], "identity": "Prcsrly.Fly.Framework.ApiTemplate", "name": "Fly Framework API", "shortName": "flyapi", "tags": {"language": "C#", "type": "project"}, "description": "A template for creating ASP.NET Core Web API projects using Prcsrly.Fly.Framework", "sourceName": "FlyApiTemplate", "preferNameDirectory": true, "symbols": {"Framework": {"type": "parameter", "description": "The target framework for the project.", "datatype": "choice", "choices": [{"choice": "net8.0", "description": "Target .NET 8.0"}], "replaces": "net8.0", "defaultValue": "net8.0"}, "UseHttps": {"type": "parameter", "datatype": "bool", "defaultValue": "true", "description": "Whether to use HTTPS"}, "UseSwagger": {"type": "parameter", "datatype": "bool", "defaultValue": "true", "description": "Whether to include Swagger/OpenAPI support"}, "UseAuthentication": {"type": "parameter", "datatype": "bool", "defaultValue": "true", "description": "Whether to include authentication"}, "DatabaseProvider": {"type": "parameter", "description": "The database provider to use", "datatype": "choice", "choices": [{"choice": "SqlServer", "description": "Use SQL Server"}, {"choice": "MySQL", "description": "Use MySQL"}, {"choice": "PostgreSQL", "description": "Use PostgreSQL"}], "defaultValue": "SqlServer", "replaces": "SqlServer"}, "IncludeSampleEntity": {"type": "parameter", "datatype": "bool", "defaultValue": "true", "description": "Whether to include a sample Product entity with full CRUD operations"}}, "sources": [{"modifiers": [{"condition": "(!IncludeSampleEntity)", "exclude": ["Entities/**", "Dtos/**", "Requests/**", "Controllers/ProductsController.cs", "Services/**"]}]}]}