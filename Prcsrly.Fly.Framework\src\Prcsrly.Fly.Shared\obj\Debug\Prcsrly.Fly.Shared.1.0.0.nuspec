﻿<?xml version="1.0" encoding="utf-8"?>
<package xmlns="http://schemas.microsoft.com/packaging/2012/06/nuspec.xsd">
  <metadata>
    <id>Prcsrly.Fly.Shared</id>
    <version>1.0.0</version>
    <authors>Prcsrly</authors>
    <license type="expression">MIT</license>
    <licenseUrl>https://licenses.nuget.org/MIT</licenseUrl>
    <projectUrl>https://github.com/prcsrly/fly-framework</projectUrl>
    <description>Application-specific shared components for Fly Framework including business domain modules, MVVM components for client applications, and shared DTOs and models.</description>
    <tags>framework shared business mvvm dto</tags>
    <repository type="git" url="https://github.com/prcsrly/fly-framework" />
    <dependencies>
      <group targetFramework="net8.0">
        <dependency id="Prcsrly.Fly.Framework.Shared" version="1.0.0" exclude="Build,Analyzers" />
        <dependency id="CommunityToolkit.Mvvm" version="8.2.2" exclude="Build,Analyzers" />
        <dependency id="FluentValidation.DependencyInjectionExtensions" version="11.10.0" exclude="Build,Analyzers" />
        <dependency id="MediatR" version="12.4.1" exclude="Build,Analyzers" />
        <dependency id="Microsoft.Extensions.Configuration.Abstractions" version="8.0.0" exclude="Build,Analyzers" />
        <dependency id="Microsoft.Extensions.DependencyInjection.Abstractions" version="8.0.1" exclude="Build,Analyzers" />
      </group>
    </dependencies>
  </metadata>
  <files>
    <file src="C:\Users\<USER>\source\repos\vscode\prcsrly\SmartiERP\Prcsrly.Fly.Framework\src\Prcsrly.Fly.Shared\bin\Debug\net8.0\Prcsrly.Fly.Shared.dll" target="lib\net8.0\Prcsrly.Fly.Shared.dll" />
  </files>
</package>