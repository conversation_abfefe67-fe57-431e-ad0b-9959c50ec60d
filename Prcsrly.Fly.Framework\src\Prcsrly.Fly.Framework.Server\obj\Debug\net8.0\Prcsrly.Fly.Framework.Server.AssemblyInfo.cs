//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Reflection;

[assembly: System.Reflection.AssemblyCompanyAttribute("Prcsrly")]
[assembly: System.Reflection.AssemblyConfigurationAttribute("Debug")]
[assembly: System.Reflection.AssemblyCopyrightAttribute("Copyright © Prcsrly 2025")]
[assembly: System.Reflection.AssemblyDescriptionAttribute(("Server-side framework components for Fly Framework including authentication, pers" +
    "istence layer abstractions, mapping configurations, and hosted services."))]
[assembly: System.Reflection.AssemblyFileVersionAttribute("*******")]
[assembly: System.Reflection.AssemblyInformationalVersionAttribute("1.0.1+fa4db3dab7210331a3ff8debc2da7a9cfa7fd702")]
[assembly: System.Reflection.AssemblyProductAttribute("Fly Framework")]
[assembly: System.Reflection.AssemblyTitleAttribute("Prcsrly.Fly.Framework.Server")]
[assembly: System.Reflection.AssemblyVersionAttribute("*******")]
[assembly: System.Reflection.AssemblyMetadataAttribute("RepositoryUrl", "https://github.com/prcsrly/fly-framework")]

// Generated by the MSBuild WriteCodeFragment class.

