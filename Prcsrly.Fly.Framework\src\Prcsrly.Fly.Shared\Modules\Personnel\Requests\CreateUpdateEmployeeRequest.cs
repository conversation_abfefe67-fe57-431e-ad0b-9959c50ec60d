using Prcsrly.Fly.Framework.Shared.Common
using Prcsrly.Fly.Framework.Shared.Common.Requests;
;

namespace Prcsrly.Fly.Shared.Modules.Personnel.Requests;

public class CreateUpdateEmployeeRequest : CreateUpdateRequestBase
{
    public string Name { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public string Phone { get; set; } = string.Empty;
    public string Identifier { get; set; } = string.Empty;
    //public Identifier AccountId { get; set; }
    public Identifier DepartmentId { get; set; }

    public decimal AccountOpeningDebtor { get; set; }
    public decimal AccountOpeningCreditor { get; set; }
}