#!/bin/bash

# Install Fly Framework API Template
# This script installs the Fly Framework API template for use with dotnet new

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
TEMPLATE_PATH="$SCRIPT_DIR/api-template"
TEMPLATE_NAME="Prcsrly.Fly.Framework.ApiTemplate"

UNINSTALL=false
FORCE=false

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --uninstall)
            UNINSTALL=true
            shift
            ;;
        --force)
            FORCE=true
            shift
            ;;
        -h|--help)
            echo "Usage: $0 [--uninstall] [--force] [--help]"
            echo "  --uninstall  Uninstall the template"
            echo "  --force      Force reinstall if already installed"
            echo "  --help       Show this help message"
            exit 0
            ;;
        *)
            echo "Unknown option: $1"
            echo "Use --help for usage information"
            exit 1
            ;;
    esac
done

echo -e "\033[36mFly Framework API Template Installer\033[0m"
echo -e "\033[36m====================================\033[0m"

if [ "$UNINSTALL" = true ]; then
    echo -e "\033[33mUninstalling template...\033[0m"
    
    if dotnet new uninstall "$TEMPLATE_NAME" 2>/dev/null; then
        echo -e "\033[32m✅ Template uninstalled successfully!\033[0m"
    else
        echo -e "\033[31m❌ Error uninstalling template\033[0m"
        exit 1
    fi
else
    # Check if template directory exists
    if [ ! -d "$TEMPLATE_PATH" ]; then
        echo -e "\033[31m❌ Template directory not found: $TEMPLATE_PATH\033[0m"
        exit 1
    fi

    # Check if template is already installed
    if dotnet new list | grep -q "flyapi" && [ "$FORCE" != true ]; then
        echo -e "\033[33m⚠️  Template appears to be already installed. Use --force to reinstall.\033[0m"
        echo -e "\033[37m   To uninstall: $0 --uninstall\033[0m"
        exit 0
    fi

    echo -e "\033[33mInstalling template from: $TEMPLATE_PATH\033[0m"
    
    # Uninstall existing template if Force is specified
    if [ "$FORCE" = true ]; then
        echo -e "\033[33mForce reinstall - uninstalling existing template...\033[0m"
        dotnet new uninstall "$TEMPLATE_NAME" 2>/dev/null || true
    fi
    
    # Install the template
    if dotnet new install "$TEMPLATE_PATH"; then
        echo -e "\033[32m✅ Template installed successfully!\033[0m"
        echo ""
        echo -e "\033[36mUsage:\033[0m"
        echo -e "\033[37m  dotnet new flyapi -n MyApiProject\033[0m"
        echo ""
        echo -e "\033[36mOptions:\033[0m"
        echo -e "\033[37m  --UseHttps true|false              Enable HTTPS (default: true)\033[0m"
        echo -e "\033[37m  --UseSwagger true|false            Include Swagger (default: true)\033[0m"
        echo -e "\033[37m  --UseAuthentication true|false     Include JWT auth (default: true)\033[0m"
        echo -e "\033[37m  --DatabaseProvider SqlServer|MySQL|PostgreSQL  Database provider (default: SqlServer)\033[0m"
        echo -e "\033[37m  --IncludeSampleEntity true|false   Include Product sample (default: true)\033[0m"
        echo ""
        echo -e "\033[36mExample:\033[0m"
        echo -e "\033[37m  dotnet new flyapi -n MyApi --DatabaseProvider MySQL --UseAuthentication false\033[0m"
    else
        echo -e "\033[31m❌ Error installing template\033[0m"
        exit 1
    fi
fi

echo ""
echo -e "\033[32mDone!\033[0m"
