using Prcsrly.Fly.Framework.Shared.Common.Attributes;
using Prcsrly.Fly.Framework.Shared.Common.Dtos;
using Prcsrly.Fly.Framework.Shared.Common;
using Prcsrly.Fly.Shared.Modules.PurchaseAndSales.Enums;

namespace Prcsrly.Fly.Shared.Modules.PurchaseAndSales.Dtos.Purchase;

//  PurchaseReturnInvoice PurchaseReturnInvoiceDetail
public class DtoPurchaseReturnInvoiceDetail : DtoBase
{
    [ClientInvisible]
    public Identifier ReturnInvoiceId { get; set; }

    [ClientInvisible]
    public Identifier UnitId { get; set; }
    public string UnitName { get; set; } = string.Empty;

    [ClientInvisible]
    public Identifier ItemId { get; set; }
    public string ItemName { get; set; } = string.Empty;
    public string ItemBarcode { get; set; } = string.Empty;

    public decimal Quantity { get; set; }
    public decimal Price { get; set; }

    public decimal Discount { get; set; }

    public DateTime? ExpireDate { get; set; }

    public decimal TotalAmount => Price * Quantity - Discount;

    public decimal CountingPrince(int mode)
    {
        var realPrice = Price * Quantity;
        if (mode == (int)DiscountType.Percent)
        {
            return realPrice - realPrice * (Discount / 100);
        }
        else if (mode == (int)DiscountType.Static)
        {
            return realPrice - Discount;
        }
        else
        {
            return realPrice;
        }
    }
}
