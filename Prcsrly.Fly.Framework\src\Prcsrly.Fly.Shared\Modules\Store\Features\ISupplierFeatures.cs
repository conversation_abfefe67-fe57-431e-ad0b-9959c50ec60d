using Prcsrly.Fly.Framework.Shared.Common.Features;
using Prcsrly.Fly.Framework.Shared.Common.Http;
using Prcsrly.Fly.Framework.Shared.Common.Requests;
using Prcsrly.Fly.Framework.Shared.Common.ValueTypes;
using Prcsrly.Fly.Shared.Modules.Accounting.Requests;
using Prcsrly.Fly.Shared.Modules.Store.Dtos;
using Prcsrly.Fly.Shared.Modules.Store.Requests;

namespace Prcsrly.Fly.Shared.Modules.Store.Features;

public interface ISupplierFeatures : IFeatureBase<DtoSupplier, SupplierQueryRequest, DeleteRequestBase, CreateUpdateSupplierRequest>
{
    ValueTask<HttpResponseWrapperList<DefaultLookup>> GetLookupAccounts(AccountQueryRequest request);
}