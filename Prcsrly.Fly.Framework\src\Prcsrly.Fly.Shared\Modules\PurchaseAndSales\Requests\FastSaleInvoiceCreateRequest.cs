using Prcsrly.Fly.Framework.Shared.Common
using Prcsrly.Fly.Framework.Shared.Common.Requests
using Prcsrly.Fly.Shared.Modules.Store.Dtos
using Newtonsoft.Json
using System.ComponentModel.DataAnnotations.Schema;

namespace Prcsrly.Fly.Shared.Modules.PurchaseAndSales.Requests;

public class FastSaleInvoiceCreateRequest : CreateUpdateRequestBase
{
    public decimal DiscountedPrice { get; set; }

    public List<ItemUnitSale> Items { get; set; } = [];

    public decimal TotalPrice { get; set; }

    public decimal SubtotalPrice { get; set; }

    public double DiscountPercentage { get; set; }

    public decimal DiscountValue { get; set; }

    public bool IsPercentageBased => DiscountPercentage > 0;
}


public class ItemUnitSale
{
    [JsonIgnore]
    public DtoItem? Item { get; set; }
    public Identifier ItemId { get; set; }
    
    [JsonIgnore]
    public DtoUnit? Unit { get; set; }
    public Identifier UnitId { get; set; }

    public decimal Quantity { get; set; }
    public decimal Price { get; set; }
}

public enum DiscountType
{
    Value,
    Percentage
}