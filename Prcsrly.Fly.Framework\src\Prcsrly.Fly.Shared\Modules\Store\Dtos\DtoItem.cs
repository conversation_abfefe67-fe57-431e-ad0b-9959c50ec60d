using Prcsrly.Fly.Framework.Shared.Common
using Prcsrly.Fly.Framework.Shared.Common.Attributes
using Prcsrly.Fly.Framework.Shared.Common.Dtos
using Prcsrly.Fly.Framework.Shared.Extensions;

namespace Prcsrly.Fly.Shared.Modules.Store.Dtos;

public class DtoItem : DtoBase
{
    [ClientInvisible]
    public Identifier CategoryId { get; set; }
    public string CategoryName { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;

    [ClientInvisible]
    public string? Photo { get; set; }
    public string? Barcode { get; set; }
    [ClientInvisible]
    public DateTime? ExpireDate { get; set; }
    public string? ExpireDateString => ExpireDate?.ToDottedDate();
    public decimal Price { get; set; }
    public decimal Cost { get; set; }

    [ClientInvisible]
    public virtual IEnumerable<DtoUnit> Units { get; set; } = [];
    [ClientInvisible]
    public virtual IEnumerable<DtoItemUnit> ItemUnits { get; set; } = [];

    public string? TheUnits
    {
        get
        {
            var def = "";

            if (ItemUnits?.Any() == true)
            {
                def = string.Join(" ,", ItemUnits.Select(s => s.UnitName));
            }

            return def;
        }
    }
}