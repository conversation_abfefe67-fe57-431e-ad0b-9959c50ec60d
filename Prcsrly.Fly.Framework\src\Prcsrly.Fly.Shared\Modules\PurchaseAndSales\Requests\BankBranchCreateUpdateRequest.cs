using Prcsrly.Fly.Framework.Shared.Common.Requests;
using Prcsrly.Fly.Framework.Shared.Common;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Prcsrly.Fly.Shared.Modules.PurchaseAndSales.Requests;

public class BankBranchCreateUpdateRequest : CreateUpdateRequestBase
{
    public Identifier BankId { get; set; }
    public string? Note { get; set; }
    public string Name { get; set; } = string.Empty;

    public decimal AccountOpeningDebtor { get; set; }
    public decimal AccountOpeningCreditor { get; set; }

}
public class BankBranchDeleteRequest : DeleteRequestBase
{

}
public class BankBranchQueryRequest : QueryRequestBase
{

}


public class InventoryCostQueryRequest : QueryRequestBase
{
    public Identifier ItemId { get; set; }
    public Identifier UnitId { get; set; }
}