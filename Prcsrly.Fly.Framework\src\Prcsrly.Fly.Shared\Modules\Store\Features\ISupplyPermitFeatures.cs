using Prcsrly.Fly.Framework.Shared.Common.Features
using Prcsrly.Fly.Framework.Shared.Common.Http
using Prcsrly.Fly.Framework.Shared.Common.Requests
using Prcsrly.Fly.Framework.Shared.Common.ValueTypes
using Prcsrly.Fly.Shared.Modules.Store.Dtos
using Prcsrly.Fly.Shared.Modules.Store.Requests;

namespace Prcsrly.Fly.Shared.Modules.Store.Features;

//  SupplyPermit

public interface ISupplyPermitFeatures : IFeatureBase<DtoSupplyPermit, SupplyPermitQueryRequest, DeleteRequestBase, CreateUpdateSupplyPermitRequest>
{
    ValueTask<HttpResponseWrapperList<DefaultLookup>> GetLookupStore(StoreQueryRequest request);
    ValueTask<HttpResponseWrapperList<DefaultLookup>> GetLookupSuppliers(SupplierQueryRequest request);
}