using Prcsrly.Fly.Framework.Shared.Common.Http;
using Prcsrly.Fly.Framework.Shared.Common.ValueTypes;
using Prcsrly.Fly.Shared.Modules.Financial.Requests;
using Prcsrly.Fly.Shared.Modules.Reports.Requests;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Prcsrly.Fly.Shared.Modules.Reports.Features;

public interface IReportsFeatures
{
    ValueTask<HttpResponseWrapperList<DefaultLookup>> GetAccounts(int itemId);
    ValueTask<HttpResponseWrapper<DynamicApiReports>> PostAccountMovementsStetements(StatementQueryRequest request);
}
