using Prcsrly.Fly.Framework.Shared.Common.Features
using Prcsrly.Fly.Framework.Shared.Common.Http
using Prcsrly.Fly.Framework.Shared.Common.Requests
using Prcsrly.Fly.Framework.Shared.Common.ValueTypes
using Prcsrly.Fly.Shared.Modules.Accounting.Requests
using Prcsrly.Fly.Shared.Modules.Personnel.Dtos
using Prcsrly.Fly.Shared.Modules.Personnel.Requests;

namespace Prcsrly.Fly.Shared.Modules.Personnel.Features;

public interface IEmployeeFeatures : IFeatureBase<DtoEmployee, EmployeeQueryRequest, DeleteRequestBase, CreateUpdateEmployeeRequest>
{
    ValueTask<HttpResponseWrapperList<DefaultLookup>> GetLookupAccounts(AccountQueryRequest request);
    ValueTask<HttpResponseWrapperList<DefaultLookup>> GetLookupDepartments(DepartmentQueryRequest request);
}