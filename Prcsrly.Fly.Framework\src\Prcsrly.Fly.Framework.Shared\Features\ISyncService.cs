using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Prcsrly.Fly.Framework.Shared.Features
{
    public interface ISyncService
    {
        Task DownloadSyncsAsync(CancellationToken cancellationToken);
        Task UploadPendingSyncsAsync(CancellationToken cancellationToken);
        Task ProcessPendingSyncsAsync(CancellationToken cancellationToken);
    }
}
