<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Prcsrly.Fly.Framework.Server</name>
    </assembly>
    <members>
        <member name="T:Prcsrly.Fly.Framework.Server.Common.Controllers.CommandsBased.CustomControllerFeatrueBased`5">
            
        </member>
        <member name="T:Prcsrly.Fly.Framework.Server.Common.Controllers.CommandsBased.CustomControllerFeatrueBased`4">
            <summary>
            Delete oprations
            </summary>
            <typeparam name="TDto"></typeparam>
            <typeparam name="TEn"></typeparam>
            <typeparam name="TQuery"></typeparam>
            <typeparam name="TDeleteCommand"></typeparam>
        </member>
    </members>
</doc>
