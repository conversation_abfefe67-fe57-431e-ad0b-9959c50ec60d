using Prcsrly.Fly.Shared.Modules.Accounting.Dtos;
using Prcsrly.Fly.Framework.Shared.Common.Requests;
using Prcsrly.Fly.Framework.Shared.Common.Features;
using Prcsrly.Fly.Shared.Modules.Accounting.Requests;
using Prcsrly.Fly.Framework.Shared.Common.Http;
using Prcsrly.Fly.Framework.Shared.Common.ValueTypes;
using Prcsrly.Fly.Framework.Shared.Common;

namespace Prcsrly.Fly.Shared.Modules.Accounting.Features;


public class AssetCreateUpdateCommand : CreateUpdateNamedRequestBase
{
    public decimal DepreciationRate { get; set; }
    public Identifier AccountId { get; set; }
}


public interface IAssetsFeatures : IFeatureBase<DtoAssets, AssetsQueryRequest, DeleteRequestBase, AssetCreateUpdateCommand>
{
    ValueTask<HttpResponseWrapperList<DefaultLookup>> GetLookupAccounts(AccountQueryRequest request);
}