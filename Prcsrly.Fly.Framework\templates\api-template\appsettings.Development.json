{
  "ConnectionStrings": {
#if (DatabaseProvider == "SqlServer")
    "DefaultConnection": "Server=(localdb)\\mssqllocaldb;Database=FlyApiTemplateDb_Dev;Trusted_Connection=true;MultipleActiveResultSets=true"
#elif (DatabaseProvider == "MySQL")
    "DefaultConnection": "Server=localhost;Database=FlyApiTemplateDb_Dev;Uid=root;Pwd=dev_password;"
#elif (DatabaseProvider == "PostgreSQL")
    "DefaultConnection": "Host=localhost;Database=FlyApiTemplateDb_Dev;Username=postgres;Password=dev_password"
#endif
  },
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning",
      "Microsoft.EntityFrameworkCore": "Information"
    }
  },
  "Serilog": {
    "MinimumLevel": {
      "Default": "Debug",
      "Override": {
        "Microsoft": "Information",
        "System": "Information"
      }
    }
  }
}
