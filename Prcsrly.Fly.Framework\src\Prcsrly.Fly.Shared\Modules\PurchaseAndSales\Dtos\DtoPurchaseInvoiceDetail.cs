using Prcsrly.Fly.Framework.Shared.Common
using Prcsrly.Fly.Framework.Shared.Common.Attributes
using Prcsrly.Fly.Framework.Shared.Common.Dtos
using Prcsrly.Fly.Framework.Shared.Extensions
using Prcsrly.Fly.Shared.Modules.PurchaseAndSales.Enums;

namespace Prcsrly.Fly.Shared.Modules.PurchaseAndSales.Dtos;

public class DtoPurchaseInvoiceDetail : DtoBase
{
    [ClientInvisible]
    public Identifier PurchaseInvoiceId { get; set; }

    [ClientInvisible]
    public Identifier UnitId { get; set; }
    public string UnitName { get; set; } = string.Empty;

    [ClientInvisible]
    public Identifier ItemId { get; set; }
    public string ItemName { get; set; } = string.Empty;
    public string ItemBarcode { get; set; } = string.Empty;

    public decimal Quantity { get; set; }
    public decimal Price { get; set; }

    public decimal Discount { get; set; }

    [ClientInvisible]
    public DateTime? ExpireDate { get; set; }
    public string? ExpireDateString => ExpireDate?.ToDottedDate();


    public decimal CountingPrince(int mode)
    {
        var realPrice = Price * Quantity;
        if (mode == (int)DiscountType.Percent)
        {
            return realPrice - realPrice * (Discount / 100);
        }
        else if (mode == (int)DiscountType.Static)
        {
            return realPrice - Discount;
        }
        else
        {
            return realPrice;
        }
    }
}