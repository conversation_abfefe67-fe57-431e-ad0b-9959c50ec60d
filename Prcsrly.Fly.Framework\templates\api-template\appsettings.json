{
  "ConnectionStrings": {
#if (DatabaseProvider == "SqlServer")
    "DefaultConnection": "Server=(localdb)\\mssqllocaldb;Database=FlyApiTemplateDb;Trusted_Connection=true;MultipleActiveResultSets=true"
#elif (DatabaseProvider == "MySQL")
    "DefaultConnection": "Server=localhost;Database=FlyApiTemplateDb;Uid=root;Pwd=your_password;"
#elif (DatabaseProvider == "PostgreSQL")
    "DefaultConnection": "Host=localhost;Database=FlyApiTemplateDb;Username=postgres;Password=your_password"
#endif
  },
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning",
      "Microsoft.EntityFrameworkCore": "Information"
    }
  },
  "Serilog": {
    "Using": ["Serilog.Sinks.Console", "Serilog.Sinks.File"],
    "MinimumLevel": {
      "Default": "Information",
      "Override": {
        "Microsoft": "Warning",
        "System": "Warning"
      }
    },
    "WriteTo": [
      {
        "Name": "Console",
        "Args": {
          "outputTemplate": "[{Timestamp:HH:mm:ss} {Level:u3}] {Message:lj} {Properties:j}{NewLine}{Exception}"
        }
      },
      {
        "Name": "File",
        "Args": {
          "path": "logs/log-.txt",
          "rollingInterval": "Day",
          "outputTemplate": "[{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} {Level:u3}] {Message:lj} {Properties:j}{NewLine}{Exception}"
        }
      }
    ]
  },
#if (UseAuthentication)
  "Authentication": {
    "JwtSettings": {
      "SecretKey": "your-super-secret-key-that-is-at-least-32-characters-long",
      "Issuer": "FlyApiTemplate",
      "Audience": "FlyApiTemplate-Users",
      "ExpirationInMinutes": 60
    }
  },
#endif
  "AllowedHosts": "*",
  "sync-settings": {
    "Enabled": false
  }
}
