//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Reflection;

[assembly: System.Reflection.AssemblyCompanyAttribute("Prcsrly")]
[assembly: System.Reflection.AssemblyConfigurationAttribute("Release")]
[assembly: System.Reflection.AssemblyCopyrightAttribute("Copyright © Prcsrly 2025")]
[assembly: System.Reflection.AssemblyDescriptionAttribute(("Core shared framework components for Fly Framework including common utilities, he" +
    "lpers, expression builders, and base exceptions."))]
[assembly: System.Reflection.AssemblyFileVersionAttribute("*******")]
[assembly: System.Reflection.AssemblyInformationalVersionAttribute("1.0.0+c929cb27293ec424cb8e4dbc229bd3ee09c6aec3")]
[assembly: System.Reflection.AssemblyProductAttribute("Fly Framework")]
[assembly: System.Reflection.AssemblyTitleAttribute("Prcsrly.Fly.Framework.Shared")]
[assembly: System.Reflection.AssemblyVersionAttribute("*******")]
[assembly: System.Reflection.AssemblyMetadataAttribute("RepositoryUrl", "https://github.com/prcsrly/fly-framework")]

// Generated by the MSBuild WriteCodeFragment class.

