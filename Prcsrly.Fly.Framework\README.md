# Prcsrly.Fly.Framework

A collection of NuGet packages extracted from the SmartiERP framework for reuse across projects.

## Packages

### Prcsrly.Fly.Framework.Shared
Core shared framework components including:
- Common utilities and helpers
- Expression builders
- Base exceptions and features
- Shared modules and extensions

### Prcsrly.Fly.Framework.Server
Server-side framework components including:
- Authentication and security
- Persistence layer abstractions
- Mapping configurations (AutoMapper/Mapster)
- Hosted services
- Server-specific extensions

### Prcsrly.Fly.Shared
Application-specific shared components including:
- Business domain modules
- MVVM components for client applications
- Shared DTOs and models

## Dependencies

- **Prcsrly.Fly.Framework.Shared**: No dependencies on other framework packages
- **Prcsrly.Fly.Framework.Server**: Depends on Prcsrly.Fly.Framework.Shared
- **Prcsrly.Fly.Shared**: Depends on Prcsrly.Fly.Framework.Shared

## Building

```bash
dotnet build
```

## Packaging

```bash
dotnet pack --configuration Release
```

## Installation

```bash
# Install the shared framework
dotnet add package Prcsrly.Fly.Framework.Shared

# Install the server framework (includes shared)
dotnet add package Prcsrly.Fly.Framework.Server

# Install the application shared components
dotnet add package Prcsrly.Fly.Shared
```
