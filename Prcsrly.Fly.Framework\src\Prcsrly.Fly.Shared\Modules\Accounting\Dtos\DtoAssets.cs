using Prcsrly.Fly.Framework.Shared.Common;
using Prcsrly.Fly.Framework.Shared.Common.Attributes;
using Prcsrly.Fly.Framework.Shared.Common.Dtos;

namespace Prcsrly.Fly.Shared.Modules.Accounting.Dtos;

public class DtoAssets : DtoBase
{
    public string Name { get; set; } = string.Empty;

    public decimal DepreciationRate { get; set; }

    [ClientInvisible]
    public Identifier AccountId { get; set; }
    public string AccountName { get; set; } = string.Empty;
}