﻿<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>

        <!-- NuGet Package Properties -->
        <PackageId>Prcsrly.Fly.Framework.Shared</PackageId>
        <Version>1.0.1</Version>
        <Authors>Prcsrly</Authors>
        <Company>Prcsrly</Company>
        <Product>Fly Framework</Product>
        <Description>Core shared framework components for Fly Framework including common utilities, helpers, expression builders, and base exceptions.</Description>
        <PackageTags>framework;shared;utilities;helpers</PackageTags>
        <PackageProjectUrl>https://github.com/prcsrly/fly-framework</PackageProjectUrl>
        <RepositoryUrl>https://github.com/prcsrly/fly-framework</RepositoryUrl>
        <RepositoryType>git</RepositoryType>
        <PackageLicenseExpression>MIT</PackageLicenseExpression>
        <GeneratePackageOnBuild>true</GeneratePackageOnBuild>
        <IncludeSymbols>true</IncludeSymbols>
        <SymbolPackageFormat>snupkg</SymbolPackageFormat>

        <!-- Assembly Properties -->
        <AssemblyName>Prcsrly.Fly.Framework.Shared</AssemblyName>
        <RootNamespace>Prcsrly.Fly.Framework.Shared</RootNamespace>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="Microsoft.Extensions.Configuration.Abstractions" Version="8.0.0" />
        <PackageReference Include="Microsoft.Extensions.DependencyInjection.Abstractions" Version="8.0.1" />
        <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    </ItemGroup>

    <ItemGroup>
      <Folder Include="Modules\Identity\Enums\" />
      <Folder Include="Modules\Identity\Features\" />
      <Folder Include="Modules\Identity\SeededData\" />
      <Folder Include="Modules\Identity\Response\" />
      <Folder Include="Modules\Identity\Requests\" />
      <Folder Include="Security\" />
    </ItemGroup>

</Project>
