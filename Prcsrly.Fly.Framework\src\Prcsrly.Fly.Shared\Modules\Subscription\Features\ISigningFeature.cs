using Prcsrly.Fly.Framework.Shared.Common.Features
using Prcsrly.Fly.Framework.Shared.Common.Http
using Prcsrly.Fly.Shared.Modules.Marketers.Response;

namespace Prcsrly.Fly.Shared.Modules.Subscription.Features;

public record SubscriperInfoResponse(string Token, string Name, string Phone, string Email, string Avatar);

public record SigningRequest(string Phone, string Password);

public interface ISigningFeature : IFeatureBase
{
    ValueTask<HttpResponseWrapper<SubscriperInfoResponse>> Signing(SigningRequest request);
    ValueTask<HttpResponseWrapper<DtoUserInfo>> GetUserInfo();
}