using Prcsrly.Fly.Framework.Shared.Common.Dtos;
using System.ComponentModel.DataAnnotations;

namespace FlyApiTemplate.Dtos;

public class ProductDto : DtoBase
{
    [Required]
    [StringLength(200, MinimumLength = 1)]
    public string Name { get; set; } = string.Empty;

    [StringLength(1000)]
    public string Description { get; set; } = string.Empty;

    [Required]
    [Range(0.01, double.MaxValue, ErrorMessage = "Price must be greater than 0")]
    public decimal Price { get; set; }

    [StringLength(50)]
    public string SKU { get; set; } = string.Empty;

    [Range(0, int.MaxValue, ErrorMessage = "Stock quantity cannot be negative")]
    public int StockQuantity { get; set; }

    public bool IsActive { get; set; } = true;

    public DateTime? LastUpdated { get; set; }

    [StringLength(100)]
    public string Category { get; set; } = string.Empty;
}
