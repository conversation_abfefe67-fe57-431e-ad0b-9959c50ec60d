using Prcsrly.Fly.Framework.Shared.Common.Features
using Prcsrly.Fly.Framework.Shared.Common.Http
using Prcsrly.Fly.Framework.Shared.Common.ValueTypes
using Prcsrly.Fly.Shared.Modules.PurchaseAndSales.Dtos.Misc
using Prcsrly.Fly.Shared.Modules.PurchaseAndSales.Requests;

namespace Prcsrly.Fly.Shared.Modules.PurchaseAndSales.Features;

public interface IBankBranchFeatures : IFeatureBase<DtoBankBranch, BankBranchQueryRequest, BankBranchDeleteRequest, BankBranchCreateUpdateRequest>
{
    ValueTask<HttpResponseWrapperList<DefaultLookup>> GetLookupBanks(BankQueryRequest request);
}