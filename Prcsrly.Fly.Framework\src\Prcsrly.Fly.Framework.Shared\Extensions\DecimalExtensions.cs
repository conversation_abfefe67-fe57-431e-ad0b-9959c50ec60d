namespace Prcsrly.Fly.Framework.Shared.Extensions
{
    public static class DecimalExtentions
    {
        public static string ToDottedCommaedNumber(this long value)
        {
            return value.ToString(("### ##0"));
        }
        public static string ToDottedCommaedNumber(this decimal value)
        {
            return value.ToString(("###,##0.##"));
        }
        public static string ToDottedCommaedNumber(this decimal? value)
        {
            return value is null ? "0" : value.Value.ToDottedCommaedNumber();
        }
        public static string ToDottedNumber(this decimal value)
        {
            return value.ToString(("##0.##"));
        }
        public static string ToDottedDate(this decimal? value)
        {
            return (value is null) ? "0" : value.Value.ToDottedCommaedNumber();
        }
    }
}
