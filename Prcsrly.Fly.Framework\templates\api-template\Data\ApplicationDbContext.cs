using Microsoft.EntityFrameworkCore;
using Prcsrly.Fly.Framework.Server.Persistence.Common;
#if (IncludeSampleEntity)
using FlyApiTemplate.Entities;
#endif

namespace FlyApiTemplate.Data;

public class ApplicationDbContext : DbCustomeContext
{
#if (IncludeSampleEntity)
    public DbSet<Product> Products { get; set; }
#endif

    public ApplicationDbContext(DbContextOptions<ApplicationDbContext> options) : base(options)
    {
    }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

#if (IncludeSampleEntity)
        // Configure Product entity
        modelBuilder.Entity<Product>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Name)
                  .IsRequired()
                  .HasMaxLength(200);
            entity.Property(e => e.Description)
                  .HasMaxLength(1000);
            entity.Property(e => e.Price)
                  .HasColumnType("decimal(18,2)");
            entity.Property(e => e.SKU)
                  .HasMaxLength(50);
            
            // Index for better performance
            entity.HasIndex(e => e.SKU).IsUnique();
            entity.HasIndex(e => e.Name);
        });
#endif

        // Add any additional entity configurations here
    }

    protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
    {
        base.OnConfiguring(optionsBuilder);
        
        if (!optionsBuilder.IsConfigured)
        {
            // Fallback configuration if needed
#if (DatabaseProvider == "SqlServer")
            optionsBuilder.UseSqlServer("Server=(localdb)\\mssqllocaldb;Database=FlyApiTemplateDb;Trusted_Connection=true;");
#elif (DatabaseProvider == "MySQL")
            optionsBuilder.UseMySql("Server=localhost;Database=FlyApiTemplateDb;Uid=root;Pwd=password;",
                ServerVersion.AutoDetect("Server=localhost;Database=FlyApiTemplateDb;Uid=root;Pwd=password;"));
#elif (DatabaseProvider == "PostgreSQL")
            optionsBuilder.UseNpgsql("Host=localhost;Database=FlyApiTemplateDb;Username=postgres;Password=password");
#endif
        }
    }
}
