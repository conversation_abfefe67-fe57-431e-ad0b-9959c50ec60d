using Ardalis.Result;
using Prcsrly.Fly.Framework.Shared.Common.Http;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Prcsrly.Fly.Framework.Server.Exctentions
{
    
    public static class HttpResponseWrapperExctentions
    {
        public static HttpResponseWrapper<T> Problem<T>(this HttpResponseWrapper<T> httpResponseWrapper, List<ValidationError> errors)
        {
        var errorString = string.Join(", ", errors.Select(x => x.ErrorMessage));
            return new HttpResponseWrapper<T>
            {
                Data = default,
                Status = false,
                StatusCode = 500,
                ErrorMessage = errorString,
            };
        }
    }
}
