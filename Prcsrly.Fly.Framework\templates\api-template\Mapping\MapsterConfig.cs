using FlyApiTemplate.Dtos;
using FlyApiTemplate.Entities;
using FlyApiTemplate.Requests;
using Mapster;

namespace FlyApiTemplate.Mapping;

public static class MapsterConfig
{
    public static void Configure()
    {
        // Entity to DTO mapping
        TypeAdapterConfig<Product, ProductDto>
            .NewConfig()
            .Map(dest => dest.Id, src => src.Guid)
            .Map(dest => dest.CreatedAt, src => src.CreatedAt)
            .Map(dest => dest.UpdatedAt, src => src.UpdatedAt);

        // DTO to Entity mapping
        TypeAdapterConfig<ProductDto, Product>
            .NewConfig()
            .Map(dest => dest.Guid, src => src.Id)
            .Map(dest => dest.CreatedAt, src => src.CreatedAt)
            .Map(dest => dest.UpdatedAt, src => src.UpdatedAt)
            .Ignore(dest => dest.Id); // Ignore database ID

        // Create command to DTO mapping
        TypeAdapterConfig<CreateProductCommand, ProductDto>
            .NewConfig()
            .Ignore(dest => dest.Id)
            .Ignore(dest => dest.CreatedAt)
            .Ignore(dest => dest.UpdatedAt);

        // Update command to DTO mapping
        TypeAdapterConfig<UpdateProductCommand, ProductDto>
            .NewConfig()
            .Map(dest => dest.Id, src => src.Id.Value)
            .Ignore(dest => dest.CreatedAt)
            .Ignore(dest => dest.UpdatedAt);

        // Create command to Entity mapping
        TypeAdapterConfig<CreateProductCommand, Product>
            .NewConfig()
            .Ignore(dest => dest.Guid)
            .Ignore(dest => dest.Id)
            .Ignore(dest => dest.CreatedAt)
            .Ignore(dest => dest.UpdatedAt)
            .Map(dest => dest.LastUpdated, src => DateTime.UtcNow);

        // Update command to Entity mapping
        TypeAdapterConfig<UpdateProductCommand, Product>
            .NewConfig()
            .Map(dest => dest.Guid, src => src.Id.Value)
            .Ignore(dest => dest.Id)
            .Ignore(dest => dest.CreatedAt)
            .Ignore(dest => dest.UpdatedAt)
            .Map(dest => dest.LastUpdated, src => DateTime.UtcNow);
    }
}
