using FlyApiTemplate.Dtos;
using MediatR;
using Prcsrly.Fly.Framework.Shared.Common.Http;
using Prcsrly.Fly.Framework.Shared.Common.Requests;
using System.ComponentModel.DataAnnotations;

namespace FlyApiTemplate.Requests;

// Query Request
public class GetProductsQuery : QueryRequestBase, IRequest<HttpResponseWrapperList<ProductDto>>
{
    public string? Category { get; set; }
    public decimal? MinPrice { get; set; }
    public decimal? MaxPrice { get; set; }
    public bool? IsActive { get; set; }
}

// Create Request
public class CreateProductCommand : CreateRequestBase, IRequest<HttpResponseWrapper<ProductDto?>>
{
    [Required]
    [StringLength(200, MinimumLength = 1)]
    public string Name { get; set; } = string.Empty;

    [StringLength(1000)]
    public string Description { get; set; } = string.Empty;

    [Required]
    [Range(0.01, double.MaxValue, ErrorMessage = "Price must be greater than 0")]
    public decimal Price { get; set; }

    [StringLength(50)]
    public string SKU { get; set; } = string.Empty;

    [Range(0, int.MaxValue, ErrorMessage = "Stock quantity cannot be negative")]
    public int StockQuantity { get; set; }

    public bool IsActive { get; set; } = true;

    [StringLength(100)]
    public string Category { get; set; } = string.Empty;
}

// Update Request
public class UpdateProductCommand : UpdateRequestBase, IRequest<HttpResponseWrapper<ProductDto?>>
{
    [Required]
    [StringLength(200, MinimumLength = 1)]
    public string Name { get; set; } = string.Empty;

    [StringLength(1000)]
    public string Description { get; set; } = string.Empty;

    [Required]
    [Range(0.01, double.MaxValue, ErrorMessage = "Price must be greater than 0")]
    public decimal Price { get; set; }

    [StringLength(50)]
    public string SKU { get; set; } = string.Empty;

    [Range(0, int.MaxValue, ErrorMessage = "Stock quantity cannot be negative")]
    public int StockQuantity { get; set; }

    public bool IsActive { get; set; } = true;

    [StringLength(100)]
    public string Category { get; set; } = string.Empty;
}

// Delete Request
public class DeleteProductCommand : DeleteRequestBase, IRequest<HttpResponseWrapper<ProductDto>>
{
    public DeleteProductCommand() { }
    
    public DeleteProductCommand(Guid id)
    {
        Id = new(id);
    }
}
