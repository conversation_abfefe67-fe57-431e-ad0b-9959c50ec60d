using Prcsrly.Fly.Framework.Shared.Common;
using Prcsrly.Fly.Shared.Modules.Accounting.Dtos;
using Prcsrly.Fly.Shared.Modules.Global.Dtos;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Prcsrly.Fly.Shared.Modules.Global.SeededData
{
    public class ConfigSeed
    {
        private const string c = "";
        private static readonly DateTime d = new(2024, 7, 2);

        public static IEnumerable<DtoConfig>? configs = new List<DtoConfig>
        {
            Create(
                 id: "adecdcbe-5c66-4069-9f41-ee29aa4a6da1"
                , SafeAccountId: "adecdcbe-5c66-4069-9f41-ee29aa4a6da1"
                , BankAccountId: "adecdcbe-5c66-4069-9f41-ee29aa4a6da2"
                , ClientsAccountId: "adecdcbe-5c66-4069-9f41-ee29aa4a6dc2"
                , ExpenseAccountId: "0E54960C-8A5B-473D-8022-C4A47C651001"
                , EmployeesAccountId: "13440fba-02cd-4f4b-843b-6159611c3fac"
                , SupplyersAccountId: "2b5ee402-49f1-444c-9a86-bf76e0ecbf90"
                , PurchaseInvoicesAccountId: "0E54960C-8A5B-473D-8022-C4A47C651002"
                , GeneralClientAccountAccountId: "adecdcbe-5c66-4069-9f41-ee29aa4a6dc3"
                , GeneralSupplierAccountAccountId: "2b5ee402-49f1-444c-9a86-bf76e0ecbf91"
                , SalesInvoicesAccountId: "0E54960C-8A5B-473D-8022-C4A47C651102"
                , MarketerInvoicesAccountId: "0E54960C-8A5B-473D-8022-C4A47C651103"
                , AssetsAccount: "ad630d24-f791-4d5a-8104-27595e273112"
                , GeneralShareAccount: "0E54960C-8A5B-473D-8022-C8847C651103"
                , DiscountAccount: "0E54960C-8A5B-473D-8022-C8847C651104"
                )
        };
        //
        //



        // Natural
        private static DtoConfig Create(string id
        , string SafeAccountId
        , string BankAccountId
        , string ClientsAccountId
        , string ExpenseAccountId
        , string EmployeesAccountId
        , string SupplyersAccountId
        , string PurchaseInvoicesAccountId
        , string GeneralClientAccountAccountId
        , string GeneralSupplierAccountAccountId
        , string SalesInvoicesAccountId
        , string MarketerInvoicesAccountId
        , string AssetsAccount
        , string GeneralShareAccount
        , string DiscountAccount
        )
        {
            return new DtoConfig
            {
                Id = Identifier.Parse(id)
               ,
                SafeAccount = Guid.Parse(SafeAccountId).ToString()
               ,
                BankAccount = Guid.Parse(BankAccountId).ToString()
               ,
                ClientsAccount = Guid.Parse(ClientsAccountId).ToString()
               ,
                ExpenseAccount = Guid.Parse(ExpenseAccountId).ToString()
               ,
                EmployeesAccount = Guid.Parse(EmployeesAccountId).ToString()
               ,
                SupplyersAccount = Guid.Parse(SupplyersAccountId).ToString()
               ,
                PurchaseInvoicesAccount = Guid.Parse(PurchaseInvoicesAccountId).ToString()
               ,
                GeneralClientAccountAccount = Guid.Parse(GeneralClientAccountAccountId).ToString()
               ,
                GeneralSupplierAccountAccount = Guid.Parse(GeneralSupplierAccountAccountId).ToString()
               ,
                SalesInvoicesAccountId = Guid.Parse(SalesInvoicesAccountId).ToString()
               ,
                MarketerInvoicesAccountId = Guid.Parse(MarketerInvoicesAccountId).ToString()
                ,
                AssetsAccount = Guid.Parse(AssetsAccount).ToString()
                ,
                GeneralShareAccount = Guid.Parse(GeneralShareAccount).ToString()
                ,
                DiscountAccount = Guid.Parse(DiscountAccount).ToString()
            };
        }
    }
}
