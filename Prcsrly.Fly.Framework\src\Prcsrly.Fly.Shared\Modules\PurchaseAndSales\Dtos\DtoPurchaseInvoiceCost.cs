using Prcsrly.Fly.Framework.Shared.Common;
using Prcsrly.Fly.Framework.Shared.Common.Attributes;
using Prcsrly.Fly.Framework.Shared.Common.Dtos;

namespace Prcsrly.Fly.Shared.Modules.PurchaseAndSales.Dtos;

public class DtoPurchaseInvoiceCost : DtoBase
{
    [ClientInvisible]
    public Identifier PurchaseInvoiceId { get; set; }

    public string Name { get; set; } = string.Empty;
    public string Notes { get; set; } = string.Empty;
    public decimal Ammount { get; set; }
}