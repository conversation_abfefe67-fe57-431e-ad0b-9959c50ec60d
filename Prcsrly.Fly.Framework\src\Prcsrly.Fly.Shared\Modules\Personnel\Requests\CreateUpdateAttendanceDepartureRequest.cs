using Prcsrly.Fly.Framework.Shared.Common;
using Prcsrly.Fly.Framework.Shared.Common.Requests;
using Prcsrly.Fly.Shared.Common;
using Prcsrly.Fly.Shared.Modules.Personnel.Enums;

namespace Prcsrly.Fly.Shared.Modules.Personnel.Requests;

public class CreateUpdateAttendanceDepartureRequest : CreateUpdateRequestBase
{
    public Identifier EmployeeId { get; set; }
    public decimal Amount { get; set; }
    public string Note { get; set; } = string.Empty;
    public AttendanceDepartureMode Mode { get; set; }
    public DateTime? Date { get; set; }
}