using Prcsrly.Fly.Framework.Shared.Common.Features;
using Prcsrly.Fly.Framework.Shared.Common.Http;
using Prcsrly.Fly.Framework.Shared.Common.Requests;
using Prcsrly.Fly.Framework.Shared.Common.ValueTypes;
using Prcsrly.Fly.Shared.Modules.Personnel.Dtos;
using Prcsrly.Fly.Shared.Modules.Personnel.Requests;

namespace Prcsrly.Fly.Shared.Modules.Personnel.Features;

public interface IEmployeeAssignmentFeatures : IFeatureBase<DtoEmployeeAssignment, EmployeeAssignmentQueryRequest, DeleteRequestBase, EmployeeAssignmentCreateUpdateRequest>
{
    ValueTask<HttpResponseWrapperList<DefaultLookup>> GetLookupEmployees(EmployeeQueryRequest request);
}