using Prcsrly.Fly.Framework.Shared.Common
using Prcsrly.Fly.Framework.Shared.Common.Requests;

namespace Prcsrly.Fly.Shared.Modules.Global.Requests;

public class RoleGroupCreateUpdate : CreateUpdateRequestBase
{
    public string Name { get; set; } = string.Empty;
    public List<Identifier> Roles { get; set; }
}

public class RoleGroupDelete : DeleteRequestBase
{

}

public class RoleGroupQuery : QueryRequestBase
{

}