using Prcsrly.Fly.Framework.Shared.Common
using Prcsrly.Fly.Framework.Shared.Common.Requests;

namespace Prcsrly.Fly.Shared.Modules.Store.Requests;

public class CreateUpdateItemRequest : CreateUpdateRequestBase
{
    public Identifier CategoryId { get; set; }
    public string Name { get; set; } = string.Empty;
    public string? Photo { get; set; }
    public string? Barcode { get; set; }
    public DateTime? ExpireDate { get; set; }
    public decimal Price { get; set; }

    public IEnumerable<CreateUpdateItemUnitRequest> ItemUnits { get; set; } = [];
}

public class DeleteItemRequest : DeleteRequestBase
{

}