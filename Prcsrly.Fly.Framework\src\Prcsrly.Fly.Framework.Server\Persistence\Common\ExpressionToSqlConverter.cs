using Prcsrly.Fly.Framework.Shared.Common;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Query;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;

namespace Prcsrly.Fly.Framework.Server.Persistence.Common;


public static class ExpressionToSqlConverter
{
    public static (string UpdateClause, string WhereClause) Convert<T>(
        Expression<Func<T, bool>> whereExpression,
        Expression<Func<SetPropertyCalls<T>, SetPropertyCalls<T>>> setPropertyCalls)
    {
        // Process SET clause
        var updateClause = ProcessSetPropertyCalls(setPropertyCalls);

        // Process WHERE clause
        var whereClause = ProcessWhereExpression(whereExpression);

        return (updateClause, whereClause);
    }
    
    public static string Convert<T>(
        Expression<Func<T, bool>> whereExpression)
    {
        // Process WHERE clause
        var whereClause = ProcessWhereExpression(whereExpression);

        return (whereClause);
    }

    private static string ProcessSetPropertyCalls<T>(Expression<Func<SetPropertyCalls<T>, SetPropertyCalls<T>>> expression)
    {
        var setParts = new List<string>();
        var visitor = new SetPropertyVisitor();
        visitor.Visit(expression);

        foreach (var (propertyName, value) in visitor.Properties)
        {
            setParts.Add($"{propertyName} = {FormatValue(value)}");
        }

        return string.Join(", ", setParts);
    }

    private static string ProcessWhereExpression<T>(Expression<Func<T, bool>> expression)
    {
        var visitor = new WhereExpressionVisitor();
        visitor.Visit(expression.Body);
        return visitor.WhereClause;
    }

    private static string FormatValue(object value)
    {
        if (value == null) return "NULL";
        if (value is string) return $"'{value.ToString().Replace("'", "''")}'";
        if (value is bool b) return b ? "1" : "0";
        if (value is DateTime dt) return $"'{dt:yyyy-MM-dd HH:mm:ss}'";
        return value.ToString();
    }

    private class SetPropertyVisitor : ExpressionVisitor
    {
        public Dictionary<string, object> Properties { get; } = new();

        protected override Expression VisitMethodCall(MethodCallExpression node)
        {
            if (node.Method.Name == "SetProperty" &&
                node.Method.DeclaringType?.Name == "SetPropertyCalls`1")
            {
                // Handle the property selector (first argument)
                if (node.Arguments[0] is LambdaExpression propertyLambda)
                {
                    if (propertyLambda.Body is MemberExpression memberExpr)
                    {
                        var propertyName = memberExpr.Member.Name;

                        // Handle the value (second argument)
                        object value = ExtractValue(node.Arguments[1]);
                        Properties[propertyName] = value;
                    }
                }
                // Also handle the case where it might be a unary expression wrapping the lambda
                else if (node.Arguments[0] is UnaryExpression unary &&
                        unary.Operand is LambdaExpression unaryLambda)
                {
                    if (unaryLambda.Body is MemberExpression memberExpr)
                    {
                        var propertyName = memberExpr.Member.Name;
                        object value = ExtractValue(node.Arguments[1]);
                        Properties[propertyName] = value;
                    }
                }
            }

            return base.VisitMethodCall(node);
        }

        private object ExtractValue(Expression valueExpression)
        {
            // Handle constant values
            if (valueExpression is ConstantExpression constant)
            {
                return constant.Value;
            }

            // Handle unary expressions (like conversions)
            if (valueExpression is UnaryExpression unary)
            {
                if (unary.Operand is ConstantExpression operandConstant)
                {
                    return operandConstant.Value;
                }
            }

            // Handle member access (like variables from closure)
            if (valueExpression is MemberExpression member)
            {
                return GetMemberValue(member);
            }

            // Handle lambda expressions for computed values
            if (valueExpression is LambdaExpression lambda)
            {
                try
                {
                    var compiled = lambda.Compile();
                    return compiled.DynamicInvoke();
                }
                catch
                {
                    return "[COMPUTED_VALUE]";
                }
            }

            // Fallback for other expression types
            try
            {
                var lambdaExpr = Expression.Lambda(valueExpression);
                return lambdaExpr.Compile().DynamicInvoke();
            }
            catch
            {
                return "[EXPRESSION]";
            }
        }

        private object GetMemberValue(MemberExpression member)
        {
            // Handle instance members
            if (member.Expression is ConstantExpression containerExpr)
            {
                var container = containerExpr.Value;
                if (member.Member is FieldInfo field)
                    return field.GetValue(container);
                if (member.Member is PropertyInfo prop)
                    return prop.GetValue(container);
            }
            // Handle static members
            else if (member.Expression == null)
            {
                if (member.Member is FieldInfo field)
                    return field.GetValue(null);
                if (member.Member is PropertyInfo prop)
                    return prop.GetValue(null);
            }

            // Try to evaluate the expression
            try
            {
                var lambda = Expression.Lambda(member);
                return lambda.Compile().DynamicInvoke();
            }
            catch
            {
                throw new NotSupportedException($"Cannot get value of member {member.Member.Name}");
            }
        }
    }

    private class WhereExpressionVisitor : ExpressionVisitor
    {
        private readonly Stack<string> _expressionStack = new Stack<string>();
        public string WhereClause => _expressionStack.Count == 1 ? _expressionStack.Peek() : "";

        protected override Expression VisitBinary(BinaryExpression node)
        {
            // Visit left and right first to build their expressions
            var left = base.Visit(node.Left);
            var right = base.Visit(node.Right);

            var leftStr = _expressionStack.Pop();
            var rightStr = _expressionStack.Pop();

            var op = node.NodeType switch
            {
                ExpressionType.Equal => "=",
                ExpressionType.NotEqual => "<>",
                ExpressionType.GreaterThan => ">",
                ExpressionType.GreaterThanOrEqual => ">=",
                ExpressionType.LessThan => "<",
                ExpressionType.LessThanOrEqual => "<=",
                ExpressionType.AndAlso => "AND",
                ExpressionType.OrElse => "OR",
                _ => throw new NotSupportedException($"Operator {node.NodeType} not supported")
            };

            _expressionStack.Push($"({leftStr} {op} {rightStr})");
            return node;
        }

        protected override Expression VisitMember(MemberExpression node)
        {
            if (node.Expression != null && node.Expression.NodeType == ExpressionType.Parameter)
            {
                _expressionStack.Push(node.Member.Name);
            }
            else
            {
                var value = GetValue(node);
                _expressionStack.Push(FormatSqlValue(value));
            }
            return node;
        }

        protected override Expression VisitConstant(ConstantExpression node)
        {
            _expressionStack.Push(FormatSqlValue(node.Value));
            return node;
        }

        protected override Expression VisitUnary(UnaryExpression node)
        {
            if (node.NodeType == ExpressionType.Convert)
            {
                // Just visit the operand and ignore the convert
                return Visit(node.Operand);
            }
            return base.VisitUnary(node);
        }

        private object GetValue(MemberExpression member)
        {
            if (member.Member is FieldInfo field)
            {
                if (field.IsStatic)
                    return field.GetValue(null);

                if (member.Expression is ConstantExpression container)
                    return field.GetValue(container.Value);
            }
            else if (member.Member is PropertyInfo prop)
            {
                if (prop.GetMethod?.IsStatic == true)
                    return prop.GetValue(null);

                if (member.Expression is ConstantExpression container)
                    return prop.GetValue(container.Value);
            }

            throw new NotSupportedException($"Cannot get value of member {member.Member.Name}");
        }

        private string FormatSqlValue(object value)
        {
            return value switch
            {
                null => "NULL",
                string s => $"'{s.Replace("'", "''")}'",
                bool b => b ? "1" : "0",
                Guid g => $"'{g}'",
                Enum e => System.Convert.ToInt32(e).ToString(),
                Identifier identifier => $"'{identifier.Value}'",
                _ => value.ToString()
            };
        }
    }

}


#region MyRegion

//public async Task<int> ExecuteUpdateAsync(
//    Expression<Func<T, bool>> expression,
//    Expression<Func<SetPropertyCalls<T>, SetPropertyCalls<T>>> setPropertyCalls,
//    CancellationToken cancellationToken = default)
//{
//    // 1. First get IDs and current values of affected entities
//    var entityType = DbSet.EntityType;
//    var primaryKey = entityType.FindPrimaryKey();
//    var pkProperty = primaryKey.Properties.First();

//    var (updateClause, whereClause) = ExpressionToSqlConverter.Convert(expression, setPropertyCalls);

//    // 2. Execute the bulk operation
//    var query = GetAll(expression);
//    var result = await query.ExecuteUpdateAsync(setPropertyCalls, cancellationToken);

//    // 3. Create sync records
//    var sync = TSync.GenerateQuery(
//        DateTime.Now, 0,
//        typeof(T).FullName,
//        EntityState.Modified,
//        null,
//        $"UPDATE {entityType.GetTableName()} SET {updateClause} WHERE {whereClause}");

//    await _aplicationContext.AddRangeAsync(sync, cancellationToken);
//    await _aplicationContext.SaveChangesAsync(cancellationToken);

//    return result;
//}

//public static (string UpdateClause, Dictionary<string, object> Properties) Parse<T>(
//    Expression<Func<SetPropertyCalls<T>, SetPropertyCalls<T>>> setPropertyCalls)
//{
//    var properties = new Dictionary<string, object>();
//    var updateParts = new List<string>();

//    ParseExpression(setPropertyCalls.Body, properties, updateParts);

//    var updateClause = string.Join(", ", updateParts);
//    return (updateClause, properties);
//}

//private static void ParseExpression(
//    Expression expression,
//    Dictionary<string, object> properties,
//    List<string> updateParts)
//{
//    if (expression is MethodCallExpression methodCall &&
//        methodCall.Method.Name == "SetProperty")
//    {
//        // Get property name
//        var propertyExpression = (MemberExpression)methodCall.Arguments[0];
//        var propertyName = propertyExpression.Member.Name;

//        // Get value expression
//        var valueExpression = methodCall.Arguments[1];

//        // Try to compile and get the value if it's a constant
//        object value = null;
//        if (valueExpression is ConstantExpression constant)
//        {
//            value = constant.Value;
//        }
//        else if (valueExpression is UnaryExpression unary &&
//                 unary.Operand is ConstantExpression operand)
//        {
//            value = operand.Value;
//        }
//        else
//        {
//            // For more complex expressions, we'd need to compile them
//            var lambda = Expression.Lambda(valueExpression);
//            value = lambda.Compile().DynamicInvoke();
//        }

//        properties[propertyName] = value;
//        updateParts.Add($"{propertyName} = {FormatValue(value)}");

//        // Continue parsing the chain
//        if (methodCall.Arguments.Count > 2)
//        {
//            ParseExpression(methodCall.Arguments[2], properties, updateParts);
//        }
//    }
//}

//private static string FormatValue(object value)
//{
//    if (value == null) return "NULL";
//    if (value is string) return $"'{value.ToString().Replace("'", "''")}'";
//    if (value is bool b) return b ? "1" : "0";
//    if (value is DateTime dt) return $"'{dt:yyyy-MM-dd HH:mm:ss}'";
//    return value.ToString();
//}

#endregion