using Prcsrly.Fly.Framework.Server.Security.Algorithms;
using Prcsrly.Fly.Framework.Shared.Common;
using Prcsrly.Fly.Framework.Shared.Modules.Identity.Dtos;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Text;
using System.Threading.Tasks;

namespace Prcsrly.Fly.Framework.Server.Common.Controllers.Helpers
{
    public class ClaimsHelper
    {
        private readonly ClaimsPrincipal _user;
        private readonly ICustomEncryptionAlgorithm _algorithm;

        public ClaimsHelper(ICustomEncryptionAlgorithm algorithm, ClaimsPrincipal user)
        {
            _user = user;
            _algorithm = algorithm;
        }

        
        public Identifier GetUserId()
        {
            var encryptedUserId = _user.FindFirst(CustomClaims.Identifier)!.Value;
            var userId = _algorithm.Decrypt(encryptedUserId);
            return Identifier.Parse(userId);
        }

        public Identifier GetId(string claimType)
        {
            var encryptedUserId = _user.FindFirst(claimType)!.Value;
            var userId = _algorithm.Decrypt(encryptedUserId);
            return Identifier.Parse(userId);
        }
        
        public string GetPhone()
        {
            var encryptedUserId = _user.FindFirst(CustomClaims.Phone)!.Value;
            var phone = _algorithm.Decrypt(encryptedUserId);
            return phone;
        }
        
        public string GetAccesser()
        {
            var encryptedUserId = _user.FindFirst(CustomClaims.Accesser)!.Value;
            var accesser = _algorithm.Decrypt(encryptedUserId);
            return accesser;
        }
    }
}
