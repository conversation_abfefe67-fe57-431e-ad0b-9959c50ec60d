using Prcsrly.Fly.Framework.Shared.Common.Features;
using Prcsrly.Fly.Framework.Shared.Common.Http;
using Prcsrly.Fly.Framework.Shared.Common.Requests;
using Prcsrly.Fly.Framework.Shared.Common.ValueTypes;
using Prcsrly.Fly.Shared.Modules.Store.Dtos;
using Prcsrly.Fly.Shared.Modules.Store.Requests;

namespace Prcsrly.Fly.Shared.Modules.Store.Features;

public interface ICategoryFeatures : IFeatureBase<DtoCategory, CategoryQueryRequest, DeleteRequestBase, CreateUpdateNamedRequestBase>
{
    ValueTask<HttpResponseWrapperList<DefaultLookup>> GetLookupCateguries(CategoryQueryRequest request);

}
