using Prcsrly.Fly.Framework.Shared.Common;
using Prcsrly.Fly.Framework.Shared.Common.Collections;
using Prcsrly.Fly.Shared.Modules.Accounting.Response;
using MediatR;

namespace Prcsrly.Fly.Shared.Modules.Accounting.Requests;

public class EntryReportRequest : IRequest<PageList<EntryReportResponse>>
{
    public DateTime? FromDate { get; set; }
    public DateTime? ToDate { get; set; }
    public Identifier? AccountId { get; set; }
}