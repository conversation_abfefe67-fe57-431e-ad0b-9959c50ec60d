name: Build and Publish NuGet Packages

on:
  push:
    branches: [ main, master ]
    tags: [ 'v*' ]
  pull_request:
    branches: [ main, master ]
  workflow_dispatch:

env:
  DOTNET_VERSION: '8.0.x'
  CONFIGURATION: Release

jobs:
  build:
    runs-on: windows-latest
    defaults:
      run:
        working-directory: ./Prcsrly.Fly.Framework

    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      with:
        fetch-depth: 0

    - name: Setup .NET
      uses: actions/setup-dotnet@v4
      with:
        dotnet-version: ${{ env.DOTNET_VERSION }}

    - name: Cache NuGet packages
      uses: actions/cache@v4
      with:
        path: ~/.nuget/packages
        key: ${{ runner.os }}-nuget-${{ hashFiles('**/Prcsrly.Fly.Framework/**/*.csproj') }}
        restore-keys: |
          ${{ runner.os }}-nuget-x

    - name: Restore dependencies
      run: dotnet restore

    - name: Build solution
      run: dotnet build --configuration ${{ env.CONFIGURATION }} --no-restore

    - name: Verify build outputs
      run: |
        Write-Host "Checking build outputs..."
        $sharedDll = "src\Prcsrly.Fly.Framework.Shared\bin\${{ env.CONFIGURATION }}\net8.0\Prcsrly.Fly.Framework.Shared.dll"
        $serverDll = "src\Prcsrly.Fly.Framework.Server\bin\${{ env.CONFIGURATION }}\net8.0\Prcsrly.Fly.Framework.Server.dll"

        if (Test-Path $sharedDll) {
          Write-Host "✓ Shared DLL found: $sharedDll"
        } else {
          Write-Host "✗ Shared DLL missing: $sharedDll"
          exit 1
        }

        if (Test-Path $serverDll) {
          Write-Host "✓ Server DLL found: $serverDll"
        } else {
          Write-Host "✗ Server DLL missing: $serverDll"
          exit 1
        }
      shell: pwsh

    - name: Run tests (if any)
      run: dotnet test --configuration ${{ env.CONFIGURATION }} --no-build --verbosity normal
      continue-on-error: true

    - name: Create artifacts directory
      run: |
        if (!(Test-Path "./artifacts/packages")) {
          New-Item -ItemType Directory -Path "./artifacts/packages" -Force
          Write-Host "Created artifacts/packages directory"
        }
      shell: pwsh

    - name: Pack NuGet packages
      run: |
        # Pack each project individually to ensure all packages are created
        Write-Host "Packing Shared project..."
        dotnet pack "src\Prcsrly.Fly.Framework.Shared\Prcsrly.Fly.Framework.Shared.csproj" --configuration ${{ env.CONFIGURATION }} --no-build --output ./artifacts/packages

        Write-Host "Packing Server project..."
        dotnet pack "src\Prcsrly.Fly.Framework.Server\Prcsrly.Fly.Framework.Server.csproj" --configuration ${{ env.CONFIGURATION }} --no-build --output ./artifacts/packages
      shell: pwsh

    - name: List created packages
      run: |
        Write-Host "Checking artifacts directory..."
        if (Test-Path "./artifacts/packages") {
          Write-Host "Directory exists. Contents:"
          Get-ChildItem "./artifacts/packages" -Recurse | ForEach-Object {
            Write-Host "  $($_.FullName) (Size: $($_.Length) bytes)"
          }
        } else {
          Write-Host "ERROR: artifacts/packages directory does not exist!"
          exit 1
        }
      shell: pwsh

    - name: Debug paths for upload
      run: |
        Write-Host "Current working directory: $(Get-Location)"
        Write-Host "Checking from repository root:"
        if (Test-Path "Prcsrly.Fly.Framework/artifacts/packages") {
          Write-Host "✓ Found: Prcsrly.Fly.Framework/artifacts/packages"
          Get-ChildItem "Prcsrly.Fly.Framework/artifacts/packages" | ForEach-Object { Write-Host "  - $($_.Name)" }
        } else {
          Write-Host "✗ Not found: Prcsrly.Fly.Framework/artifacts/packages"
        }

        Write-Host "Checking relative path:"
        if (Test-Path "./Prcsrly.Fly.Framework/artifacts/packages") {
          Write-Host "✓ Found: ./Prcsrly.Fly.Framework/artifacts/packages"
          Get-ChildItem "./Prcsrly.Fly.Framework/artifacts/packages" | ForEach-Object { Write-Host "  - $($_.Name)" }
        } else {
          Write-Host "✗ Not found: ./Prcsrly.Fly.Framework/artifacts/packages"
        }
      shell: pwsh
      working-directory: .

    - name: Upload build artifacts
      uses: actions/upload-artifact@v4
      with:
        name: nuget-packages
        path: Prcsrly.Fly.Framework/artifacts/packages/
        if-no-files-found: error

  publish:
    needs: build
    runs-on: windows-latest
    defaults:
      run:
        working-directory: ./Prcsrly.Fly.Framework
    if: success() && github.event_name == 'push' && (github.ref == 'refs/heads/main' || github.ref == 'refs/heads/master' || startsWith(github.ref, 'refs/tags/v'))

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup .NET
      uses: actions/setup-dotnet@v4
      with:
        dotnet-version: ${{ env.DOTNET_VERSION }}

    - name: Download build artifacts
      uses: actions/download-artifact@v4
      with:
        name: nuget-packages
        path: Prcsrly.Fly.Framework/artifacts/packages

    - name: List downloaded packages
      run: |
        Write-Host "Downloaded packages:"
        if (Test-Path "./artifacts/packages") {
          Get-ChildItem "./artifacts/packages" -Recurse | ForEach-Object {
            Write-Host "  $($_.Name) (Size: $($_.Length) bytes)"
          }
        } else {
          Write-Host "ERROR: No packages found in ./artifacts/packages"
          exit 1
        }
      shell: pwsh

    - name: Add GitHub NuGet source
      run: dotnet nuget add source --username ${{ github.actor }} --password ${{ secrets.GH_TOKEN }} --store-password-in-clear-text --name github "https://nuget.pkg.github.com/${{ github.repository_owner }}/index.json"

    - name: Publish to GitHub Packages
      run: |
        Get-ChildItem "./artifacts/packages/*.nupkg" | ForEach-Object {
          Write-Host "Publishing $($_.Name)"
          dotnet nuget push $_.FullName --source "github" --api-key ${{ secrets.GH_TOKEN }} --skip-duplicate
        }
      shell: pwsh

    - name: Publish to NuGet.org (on tag)
      if: startsWith(github.ref, 'refs/tags/v')
      run: |
        Get-ChildItem "./artifacts/packages/*.nupkg" | ForEach-Object {
          Write-Host "Publishing $($_.Name) to NuGet.org"
          dotnet nuget push $_.FullName --source https://api.nuget.org/v3/index.json --api-key ${{ secrets.GH_TOKEN }} --skip-duplicate
        }
      shell: pwsh
dotnet nuget add source --username Islam-alshiki --password **************************************** --store-password-in-clear-text --name github "https://nuget.pkg.github.com/new-processor-co/index.json