name: Build and Publish NuGet Packages

on:
  push:
    branches: [ main, master ]
    tags: [ 'v*' ]
  pull_request:
    branches: [ main, master ]
  workflow_dispatch:

env:
  DOTNET_VERSION: '8.0.x'
  CONFIGURATION: Release

jobs:
  build:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      with:
        fetch-depth: 0

    - name: Setup .NET
      uses: actions/setup-dotnet@v4
      with:
        dotnet-version: ${{ env.DOTNET_VERSION }}

    - name: Cache NuGet packages
      uses: actions/cache@v4
      with:
        path: ~/.nuget/packages
        key: ${{ runner.os }}-nuget-${{ hashFiles('**/*.csproj') }}
        restore-keys: |
          ${{ runner.os }}-nuget-

    - name: Restore dependencies
      run: dotnet restore

    - name: Build solution
      run: dotnet build --configuration ${{ env.CONFIGURATION }} --no-restore

    - name: Run tests (if any)
      run: dotnet test --configuration ${{ env.CONFIGURATION }} --no-build --verbosity normal
      continue-on-error: true

    - name: Pack NuGet packages
      run: dotnet pack --configuration ${{ env.CONFIGURATION }} --no-build --output ./artifacts/packages

    - name: Upload build artifacts
      uses: actions/upload-artifact@v4
      with:
        name: nuget-packages
        path: ./artifacts/packages/*.nupkg

  publish:
    needs: build
    runs-on: ubuntu-latest
    if: github.event_name == 'push' && (github.ref == 'refs/heads/main' || github.ref == 'refs/heads/master' || startsWith(github.ref, 'refs/tags/v'))
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup .NET
      uses: actions/setup-dotnet@v4
      with:
        dotnet-version: ${{ env.DOTNET_VERSION }}

    - name: Download build artifacts
      uses: actions/download-artifact@v4
      with:
        name: nuget-packages
        path: ./artifacts/packages

    - name: Add GitHub NuGet source
      run: dotnet nuget add source --username ${{ github.actor }} --password ${{ secrets.GITHUB_TOKEN }} --store-password-in-clear-text --name github "https://nuget.pkg.github.com/${{ github.repository_owner }}/index.json"

    - name: Publish to GitHub Packages
      run: |
        for package in ./artifacts/packages/*.nupkg; do
          echo "Publishing $package"
          dotnet nuget push "$package" --source "github" --api-key ${{ secrets.GITHUB_TOKEN }} --skip-duplicate
        done

    - name: Publish to NuGet.org (on tag)
      if: startsWith(github.ref, 'refs/tags/v')
      run: |
        for package in ./artifacts/packages/*.nupkg; do
          echo "Publishing $package to NuGet.org"
          dotnet nuget push "$package" --source https://api.nuget.org/v3/index.json --api-key ${{ secrets.NUGET_API_KEY }} --skip-duplicate
        done
