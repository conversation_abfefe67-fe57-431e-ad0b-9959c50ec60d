name: Build and Publish NuGet Packages

on:
  push:
    branches: [ main, master ]
    tags: [ 'v*' ]
  pull_request:
    branches: [ main, master ]
  workflow_dispatch:

env:
  DOTNET_VERSION: '8.0.x'
  CONFIGURATION: Release

jobs:
  build:
    runs-on: windows-latest
    defaults:
      run:
        working-directory: ./Prcsrly.Fly.Framework

    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      with:
        fetch-depth: 0

    - name: Setup .NET
      uses: actions/setup-dotnet@v4
      with:
        dotnet-version: ${{ env.DOTNET_VERSION }}

    - name: Cache NuGet packages
      uses: actions/cache@v4
      with:
        path: ~/.nuget/packages
        key: ${{ runner.os }}-nuget-${{ hashFiles('**/Prcsrly.Fly.Framework/**/*.csproj') }}
        restore-keys: |
          ${{ runner.os }}-nuget-x

    - name: Restore dependencies
      run: dotnet restore

    - name: Build solution
      run: dotnet build --configuration ${{ env.CONFIGURATION }} --no-restore

    - name: Run tests (if any)
      run: dotnet test --configuration ${{ env.CONFIGURATION }} --no-build --verbosity normal
      continue-on-error: true

    - name: Pack NuGet packages
      run: |
        # Pack each project individually to ensure all packages are created
        dotnet pack "src/Prcsrly.Fly.Framework.Shared/Prcsrly.Fly.Framework.Shared.csproj" --configuration ${{ env.CONFIGURATION }} --no-build --output ./artifacts/packages
        dotnet pack "src/Prcsrly.Fly.Framework.Server/Prcsrly.Fly.Framework.Server.csproj" --configuration ${{ env.CONFIGURATION }} --no-build --output ./artifacts/packages

    - name: List created packages
      run: |
        Write-Host "Packages created:"
        Get-ChildItem "./artifacts/packages" -Recurse | ForEach-Object { Write-Host "  $($_.FullName)" }
      shell: pwsh

    - name: Upload build artifacts
      uses: actions/upload-artifact@v4
      with:
        name: nuget-packages
        path: ./artifacts/packages/
        if-no-files-found: error

  publish:
    needs: build
    runs-on: windows-latest
    defaults:
      run:
        working-directory: ./Prcsrly.Fly.Framework
    if: success() && github.event_name == 'push' && (github.ref == 'refs/heads/main' || github.ref == 'refs/heads/master' || startsWith(github.ref, 'refs/tags/v'))

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup .NET
      uses: actions/setup-dotnet@v4
      with:
        dotnet-version: ${{ env.DOTNET_VERSION }}

    - name: Download build artifacts
      uses: actions/download-artifact@v4
      with:
        name: nuget-packages
        path: ./artifacts/packages

    - name: List downloaded packages
      run: |
        Write-Host "Downloaded packages:"
        Get-ChildItem "./artifacts/packages" -Recurse | ForEach-Object { Write-Host "  $($_.FullName)" }
      shell: pwsh

    - name: Add GitHub NuGet source
      run: dotnet nuget add source --username ${{ github.actor }} --password ${{ secrets.GITHUB_TOKEN }} --store-password-in-clear-text --name github "https://nuget.pkg.github.com/${{ github.repository_owner }}/index.json"

    - name: Publish to GitHub Packages
      run: |
        Get-ChildItem "./artifacts/packages/*.nupkg" | ForEach-Object {
          Write-Host "Publishing $($_.Name)"
          dotnet nuget push $_.FullName --source "github" --api-key ${{ secrets.GITHUB_TOKEN }} --skip-duplicate
        }
      shell: pwsh

    - name: Publish to NuGet.org (on tag)
      if: startsWith(github.ref, 'refs/tags/v')
      run: |
        Get-ChildItem "./artifacts/packages/*.nupkg" | ForEach-Object {
          Write-Host "Publishing $($_.Name) to NuGet.org"
          dotnet nuget push $_.FullName --source https://api.nuget.org/v3/index.json --api-key ${{ secrets.NUGET_API_KEY }} --skip-duplicate
        }
      shell: pwsh
