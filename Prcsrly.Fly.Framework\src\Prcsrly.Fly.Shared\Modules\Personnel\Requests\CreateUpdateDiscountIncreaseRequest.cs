using Prcsrly.Fly.Framework.Shared.Common
using Prcsrly.Fly.Framework.Shared.Common.Requests;

using Prcsrly.Fly.Shared.Modules.Personnel.Enums;

namespace Prcsrly.Fly.Shared.Modules.Personnel.Requests;

public class CreateUpdateDiscountIncreaseRequest : CreateUpdateRequestBase
{
    public Identifier EmployeeId { get; set; }
    public decimal Amount { get; set; }
    public string Note { get; set; } = string.Empty;
    public DiscountIncreaseMode Mode { get; set; }
    public DateTime? Date { get; set; }
}