using Prcsrly.Fly.Framework.Shared.Common.Attributes;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System.Text;


namespace Prcsrly.Fly.Framework.Shared.Common.Dtos;

public class DtoBase
{

    [ClientInvisible]
    public Identifier Id { get; set; }


    [ClientInvisible]
    public DateTime CreateDate { get; set; }
    [ClientInvisible]
    public string? CreatedByName { get; set; }
    //public Guid Guid { get; set; }

    public bool IsNew() => Id == Identifier.Empty;

    public override string ToString()
    {

        #region old
        /*
        StringBuilder stringBuilder = new StringBuilder();
        var prps = GetType().GetProperties();
        foreach (var p in prps)
        {
            stringBuilder.AppendLine();
            string vl = "UNKown";
            try
            {
                vl = p.GetValue(this)?.ToString() ?? "NULL";
            }
            catch { }
            stringBuilder.AppendFormat("{0}={1}", p.Name, vl);
        }
        return stringBuilder.ToString();
        */
        #endregion

        return JsonConvert.SerializeObject(this, Formatting.Indented);

    }

    public override bool Equals(object? obj)
    {
        if (obj is DtoBase dto)
        {
            return this.Id == dto.Id;
        }
        return base.Equals(obj);
    }

    public override int GetHashCode()
    {
        return Id.GetHashCode();
    }


    [ClientInvisible]
    public Identifier TenantId { get; set; }
}