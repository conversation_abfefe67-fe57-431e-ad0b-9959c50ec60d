using Prcsrly.Fly.Framework.Shared.Common.Features;
using Prcsrly.Fly.Framework.Shared.Common.Http;
using Prcsrly.Fly.Framework.Shared.Common.Requests;
using Prcsrly.Fly.Framework.Shared.Common.ValueTypes;
using Prcsrly.Fly.Shared.Modules.Accounting.Requests;
using Prcsrly.Fly.Shared.Modules.PurchaseAndSales.Dtos.Misc;
using Prcsrly.Fly.Shared.Modules.PurchaseAndSales.Requests;

namespace Prcsrly.Fly.Shared.Modules.PurchaseAndSales.Features;

public interface IClientFeatures : IFeatureBase<DtoClient, ClientQueryRequest, DeleteRequestBase, ClientCreateUpdateRequest>
{
    ValueTask<HttpResponseWrapperList<DefaultLookup>> GetLookupAccounts(AccountQueryRequest request);
}
