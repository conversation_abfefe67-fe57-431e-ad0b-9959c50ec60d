using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json.Serialization;
using System.Text.Json;
using System.Threading.Tasks;
using Prcsrly.Fly.Framework.Server.Persistence;
using Prcsrly.Fly.Framework.Server.Persistence.Common;
using Microsoft.Extensions.Options;
using Prcsrly.Fly.Framework.Server.Settings;
using Prcsrly.Fly.Framework.Shared.Features;
using System.Net.Http.Json;

namespace Prcsrly.Fly.Framework.Server.Common.Sync
{

    public class SyncService(
        IRepositryBase<TSync> repositry
            , IHttpClientFactory httpClientFactory
            , ILogger<SyncService> logger
            , IOptions<SyncSettings> configureOptions) : ISyncService
    {

        private readonly ILogger<SyncService> _logger = logger;
        private readonly IRepositryBase<TSync> _repositry = repositry;
        private readonly SyncSettings _option = configureOptions.Value;
        private readonly IHttpClientFactory _httpClientFactory = httpClientFactory;

        private HttpClient HttpClient => _httpClientFactory.CreateClient("sync-http-client");

        public async Task DownloadSyncsAsync(CancellationToken cancellationToken)
        {
            try
            {
                if (_option.Server is null)
                {
                    _logger.LogWarning("Sync server is not configured");
                    return;
                }

                var httpClient = HttpClient;
                httpClient.BaseAddress = new Uri(_option.Server);

                var response = await httpClient.GetAsync("/api/get-sync", cancellationToken);

                response.EnsureSuccessStatusCode();
                var serverPackets = await response.Content.ReadFromJsonAsync<List<TSync>>(cancellationToken);
                if (serverPackets is null || !serverPackets.Any())
                {
                    _logger.LogDebug("No server sync records found");
                    return;
                }

                // Mark as applied
                foreach (var syncRecord in serverPackets)
                {
                    syncRecord.IsSync = false;
                    await _repositry.Add(syncRecord, false);
                }
                await _repositry.Save();

            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error downloading sync records");
                throw;
            }
        }

        public async Task ProcessPendingSyncsAsync(CancellationToken cancellationToken)
        {
            // Get pending sync records (batched for performance)
            var pendingSyncsQuery = _repositry.GetAll(x => !x.IsSync, orderBy: s => s.FingerPrint, page: 1, limit: 100);
            var pendingSyncs = await _repositry.ApplyList(pendingSyncsQuery);


            if (!pendingSyncs.Any())
            {
                _logger.LogDebug("No pending sync records found");
                return;
            }

            foreach (var syncRecord in pendingSyncs)
            {
                if (syncRecord.SyncObjectType == SyncObjectType.Object)
                {
                    
                }
                else if (syncRecord.SyncObjectType == SyncObjectType.Query)
                {
                    
                }
                syncRecord.IsSync = true;
                await _repositry.Update(syncRecord, false);
            }
        }

        public async Task UploadPendingSyncsAsync(CancellationToken cancellationToken)
        {
            try
            {
                // Get pending sync records (batched for performance)
                var pendingSyncsQuery = _repositry.GetAll(x => !x.IsSync, orderBy: s => s.FingerPrint, page: 1, limit: 100);
                var pendingSyncs = await _repositry.ApplyList(pendingSyncsQuery);


                if (!pendingSyncs.Any())
                {
                    _logger.LogDebug("No pending sync records found");
                    return;
                }

                _logger.LogInformation("Processing {Count} pending sync records", pendingSyncs.Count());

                if (_option.Server is null)
                {
                    _logger.LogWarning("Sync server is not configured");
                    return;
                }

                var httpClient = HttpClient;
                httpClient.BaseAddress = new Uri(_option.Server);

                // Serialize with ignored Details property
                var options = new JsonSerializerOptions
                {
                    DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull,
                    Converters = { new JsonStringEnumConverter() }
                };
                var json = JsonSerializer.Serialize(pendingSyncs, options);

                // Send to server
                var response = await httpClient.PostAsync("/api/sync",
                    new StringContent(json, Encoding.UTF8, "application/json"),
                    cancellationToken);

                response.EnsureSuccessStatusCode();

                // Mark as applied
                foreach (var syncRecord in pendingSyncs)
                {
                    syncRecord.IsSync = true;
                    await _repositry.Update(syncRecord, false);
                }
                await _repositry.Save();
                //await _dbContext.SaveChangesAsync(cancellationToken);
                _logger.LogInformation("Successfully processed {Count} sync records", pendingSyncs.Count());
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error Uploading sync records");
                throw;
            }
        }
    
    }
}
