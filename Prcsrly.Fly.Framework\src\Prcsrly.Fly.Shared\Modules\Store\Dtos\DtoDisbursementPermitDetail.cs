using Prcsrly.Fly.Framework.Shared.Common;
using Prcsrly.Fly.Framework.Shared.Common.Attributes;
using Prcsrly.Fly.Framework.Shared.Common.Dtos;

namespace Prcsrly.Fly.Shared.Modules.Store.Dtos;

public class DtoDisbursementPermitDetail : DtoBase
{
    [ClientInvisible]
    public Identifier DisbursementPermitId { get; set; }
    [ClientInvisible]
    public Identifier ItemId { get; set; }
    [ClientInvisible]
    public Identifier UnitId { get; set; }

    public string UnitName { get; set; }
    public string ItemName { get; set; }
    public decimal Quantity { get; set; }
    public DateTime ExpireDate { get; set; }
}