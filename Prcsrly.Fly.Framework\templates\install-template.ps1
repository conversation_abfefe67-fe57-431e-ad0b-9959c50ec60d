# Install Fly Framework API Template
# This script installs the Fly Framework API template for use with dotnet new

param(
    [switch]$Uninstall,
    [switch]$Force
)

$templatePath = Join-Path $PSScriptRoot "api-template"
$templateName = "Prcsrly.Fly.Framework.ApiTemplate"

Write-Host "Fly Framework API Template Installer" -ForegroundColor Cyan
Write-Host "====================================" -ForegroundColor Cyan

if ($Uninstall) {
    Write-Host "Uninstalling template..." -ForegroundColor Yellow
    
    try {
        dotnet new uninstall $templateName
        Write-Host "✅ Template uninstalled successfully!" -ForegroundColor Green
    }
    catch {
        Write-Host "❌ Error uninstalling template: $($_.Exception.Message)" -ForegroundColor Red
        exit 1
    }
}
else {
    # Check if template directory exists
    if (-not (Test-Path $templatePath)) {
        Write-Host "❌ Template directory not found: $templatePath" -ForegroundColor Red
        exit 1
    }

    # Check if template is already installed
    $installedTemplates = dotnet new list | Out-String
    if ($installedTemplates -match "flyapi" -and -not $Force) {
        Write-Host "⚠️  Template appears to be already installed. Use -Force to reinstall." -ForegroundColor Yellow
        Write-Host "   To uninstall: .\install-template.ps1 -Uninstall" -ForegroundColor Gray
        exit 0
    }

    Write-Host "Installing template from: $templatePath" -ForegroundColor Yellow
    
    try {
        # Uninstall existing template if Force is specified
        if ($Force) {
            Write-Host "Force reinstall - uninstalling existing template..." -ForegroundColor Yellow
            dotnet new uninstall $templateName 2>$null
        }
        
        # Install the template
        dotnet new install $templatePath
        
        Write-Host "✅ Template installed successfully!" -ForegroundColor Green
        Write-Host ""
        Write-Host "Usage:" -ForegroundColor Cyan
        Write-Host "  dotnet new flyapi -n MyApiProject" -ForegroundColor White
        Write-Host ""
        Write-Host "Options:" -ForegroundColor Cyan
        Write-Host "  --UseHttps true|false              Enable HTTPS (default: true)" -ForegroundColor Gray
        Write-Host "  --UseSwagger true|false            Include Swagger (default: true)" -ForegroundColor Gray
        Write-Host "  --UseAuthentication true|false     Include JWT auth (default: true)" -ForegroundColor Gray
        Write-Host "  --DatabaseProvider SqlServer|MySQL|PostgreSQL  Database provider (default: SqlServer)" -ForegroundColor Gray
        Write-Host "  --IncludeSampleEntity true|false   Include Product sample (default: true)" -ForegroundColor Gray
        Write-Host ""
        Write-Host "Example:" -ForegroundColor Cyan
        Write-Host "  dotnet new flyapi -n MyApi --DatabaseProvider MySQL --UseAuthentication false" -ForegroundColor White
        
    }
    catch {
        Write-Host "❌ Error installing template: $($_.Exception.Message)" -ForegroundColor Red
        exit 1
    }
}

Write-Host ""
Write-Host "Done!" -ForegroundColor Green
