﻿<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>

        <!-- NuGet Package Properties -->
        <PackageId>Prcsrly.Fly.Framework.Server</PackageId>
        <Version>1.0.1</Version>
        <Authors>Prcsrly</Authors>
        <Company>Prcsrly</Company>
        <Product>Fly Framework</Product>
        <Description>Server-side framework components for Fly Framework including authentication, persistence layer abstractions, mapping configurations, and hosted services.</Description>
        <PackageTags>framework;server;authentication;persistence;mapping</PackageTags>
        <PackageProjectUrl>https://github.com/prcsrly/fly-framework</PackageProjectUrl>
        <RepositoryUrl>https://github.com/prcsrly/fly-framework</RepositoryUrl>
        <RepositoryType>git</RepositoryType>
        <PackageLicenseExpression>MIT</PackageLicenseExpression>
        <GeneratePackageOnBuild>true</GeneratePackageOnBuild>
        <IncludeSymbols>true</IncludeSymbols>
        <SymbolPackageFormat>snupkg</SymbolPackageFormat>

        <!-- Assembly Properties -->
        <AssemblyName>Prcsrly.Fly.Framework.Server</AssemblyName>
        <RootNamespace>Prcsrly.Fly.Framework.Server</RootNamespace>
    </PropertyGroup>

    <ItemGroup>
      <Compile Remove="Controllers\**" />
      <EmbeddedResource Remove="Controllers\**" />
      <None Remove="Controllers\**" />
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\Prcsrly.Fly.Framework.Shared\Prcsrly.Fly.Framework.Shared.csproj" />
    </ItemGroup>

    <ItemGroup>
      <PackageReference Include="Ardalis.Result.FluentValidation" Version="10.0.0" />
      <PackageReference Include="AutoMapper" Version="13.0.1" />
      <PackageReference Include="AutoMapper.Extensions.ExpressionMapping" Version="7.0.2" />
      <PackageReference Include="FluentValidation.DependencyInjectionExtensions" Version="11.10.0" />
      <PackageReference Include="LinqKit.Core" Version="1.2.7" />
      <PackageReference Include="Mapster" Version="7.4.0" />
      <PackageReference Include="MediatR" Version="12.4.1" />
      <PackageReference Include="Microsoft.EntityFrameworkCore" Version="8.0.11" />
      <PackageReference Include="Microsoft.EntityFrameworkCore.Relational" Version="8.0.11" />
    </ItemGroup>
    <ItemGroup>
        <FrameworkReference Include="Microsoft.AspNetCore.App" />
    </ItemGroup>
    <ItemGroup>
      <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="8.0.8" />
      <PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="8.0.8" />
      <PackageReference Include="Microsoft.Extensions.Options.ConfigurationExtensions" Version="8.0.0" />
      <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
      <PackageReference Include="Pomelo.EntityFrameworkCore.MySql" Version="8.0.2" />
      <PackageReference Include="Swashbuckle.AspNetCore" Version="6.7.3" />
    </ItemGroup>
    <ItemGroup>
      <Folder Include="Mapping\AutomapperConfig\" />
      <Folder Include="Mapping\MapsterConfig\" />
    </ItemGroup>

</Project>
