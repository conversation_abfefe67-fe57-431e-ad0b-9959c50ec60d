using Prcsrly.Fly.Framework.Shared.Common;
using Prcsrly.Fly.Framework.Shared.Common.Requests;

namespace Prcsrly.Fly.Shared.Modules.Global.Requests;

public class UserCreateUpdate : CreateUpdateRequestBase
{
    public string Name { get; set; } = string.Empty;
    public string Phone { get; set; } = string.Empty;
    public string Password { get; set; } = string.Empty;
    public Identifier RoleGroupId { get; set; }
}

public class UserRoleCreateUpdate : CreateUpdateRequestBase
{

}

public class UserDelete : DeleteRequestBase
{

}

public class UserQuery : QueryRequestBase
{

}
