dotnet nuget add source --username <PERSON><PERSON><PERSON><PERSON><PERSON> --password **************************************** --store-password-in-clear-text --name github "https://nuget.pkg.github.com/new-processor-co/index.json

dotnet pack Prcsrly.Fly.Framework.Shared.csproj --configuration Release --output "../../artifacts/packages"

dotnet nuget push Prcsrly.Fly.Framework.Shared.1.0.1.nupkg --source "github" --api-key **************************************** --skip-duplicate