﻿<?xml version="1.0" encoding="utf-8"?>
<package xmlns="http://schemas.microsoft.com/packaging/2012/06/nuspec.xsd">
  <metadata>
    <id>Prcsrly.Fly.Framework.Server</id>
    <version>1.0.0</version>
    <authors>Prcsrly</authors>
    <license type="expression">MIT</license>
    <licenseUrl>https://licenses.nuget.org/MIT</licenseUrl>
    <projectUrl>https://github.com/prcsrly/fly-framework</projectUrl>
    <description>Server-side framework components for Fly Framework including authentication, persistence layer abstractions, mapping configurations, and hosted services.</description>
    <copyright>Copyright © Prcsrly 2025</copyright>
    <tags>framework server authentication persistence mapping</tags>
    <repository type="git" url="https://github.com/prcsrly/fly-framework" commit="c929cb27293ec424cb8e4dbc229bd3ee09c6aec3" />
    <dependencies>
      <group targetFramework="net8.0">
        <dependency id="Prcsrly.Fly.Framework.Shared" version="1.0.0" exclude="Build,Analyzers" />
        <dependency id="Ardalis.Result.FluentValidation" version="10.0.0" exclude="Build,Analyzers" />
        <dependency id="AutoMapper" version="13.0.1" exclude="Build,Analyzers" />
        <dependency id="AutoMapper.Extensions.ExpressionMapping" version="7.0.2" exclude="Build,Analyzers" />
        <dependency id="FluentValidation.DependencyInjectionExtensions" version="11.10.0" exclude="Build,Analyzers" />
        <dependency id="LinqKit.Core" version="1.2.7" exclude="Build,Analyzers" />
        <dependency id="Mapster" version="7.4.0" exclude="Build,Analyzers" />
        <dependency id="MediatR" version="12.4.1" exclude="Build,Analyzers" />
        <dependency id="Microsoft.AspNetCore.Authentication.JwtBearer" version="8.0.8" exclude="Build,Analyzers" />
        <dependency id="Microsoft.AspNetCore.OpenApi" version="8.0.8" exclude="Build,Analyzers" />
        <dependency id="Microsoft.EntityFrameworkCore" version="8.0.11" exclude="Build,Analyzers" />
        <dependency id="Microsoft.EntityFrameworkCore.Relational" version="8.0.11" exclude="Build,Analyzers" />
        <dependency id="Microsoft.Extensions.Options.ConfigurationExtensions" version="8.0.0" exclude="Build,Analyzers" />
        <dependency id="Newtonsoft.Json" version="13.0.3" exclude="Build,Analyzers" />
        <dependency id="Pomelo.EntityFrameworkCore.MySql" version="8.0.2" exclude="Build,Analyzers" />
        <dependency id="Swashbuckle.AspNetCore" version="6.7.3" exclude="Build,Analyzers" />
      </group>
    </dependencies>
    <frameworkReferences>
      <group targetFramework="net8.0">
        <frameworkReference name="Microsoft.AspNetCore.App" />
      </group>
    </frameworkReferences>
  </metadata>
  <files>
    <file src="C:\Users\<USER>\source\repos\vscode\prcsrly\SmartiERP\Prcsrly.Fly.Framework\src\Prcsrly.Fly.Framework.Server\bin\Release\net8.0\Prcsrly.Fly.Framework.Server.pdb" target="lib\net8.0\Prcsrly.Fly.Framework.Server.pdb" />
  </files>
</package>