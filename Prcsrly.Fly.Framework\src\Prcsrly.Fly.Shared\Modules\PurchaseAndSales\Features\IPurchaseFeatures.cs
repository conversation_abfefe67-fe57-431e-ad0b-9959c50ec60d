using Prcsrly.Fly.Framework.Shared.Common.Features;
using Prcsrly.Fly.Framework.Shared.Common.Http;
using Prcsrly.Fly.Framework.Shared.Common.Requests;
using Prcsrly.Fly.Framework.Shared.Common.ValueTypes;
using Prcsrly.Fly.Shared.Modules.PurchaseAndSales.Dtos;
using Prcsrly.Fly.Shared.Modules.PurchaseAndSales.Requests;
using Prcsrly.Fly.Shared.Modules.Store.Requests;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Prcsrly.Fly.Shared.Modules.PurchaseAndSales.Features;

public interface IPurchaseFeatures : IFeatureBase<DtoPurchaseInvoice, PurchaseInvoiceQueryRequest, DeleteRequestBase, PurchaseInvoiceCreateRequest>
{
    ValueTask<HttpResponseWrapperList<DefaultLookup>> GetLookupItems(ItemQueryRequest request);
    ValueTask<HttpResponseWrapperList<DefaultLookup>> GetLookupUnits(UnitQueryRequest request);
    ValueTask<HttpResponseWrapperList<DefaultLookup>> GetLookupStores(StoreQueryRequest request);
    ValueTask<HttpResponseWrapperList<DefaultLookup>> GetLookupSuppliers(SupplierQueryRequest request); 
    ValueTask<HttpResponseWrapper<long>> GetNextNumber();

}

