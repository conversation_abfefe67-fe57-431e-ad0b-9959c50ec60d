using FlyApiTemplate.Dtos;
using FlyApiTemplate.Entities;
using FlyApiTemplate.Requests;
using FlyApiTemplate.Services;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Prcsrly.Fly.Framework.Server.Common.Controllers.CQRSBased;
using Prcsrly.Fly.Framework.Server.Common.Localization;
using Prcsrly.Fly.Framework.Shared.Common.Http;

namespace FlyApiTemplate.Controllers;

[ApiController]
[Route("api/[controller]")]
#if DEBUG
[AllowAnonymous]
#else
[Authorize]
#endif
public class ProductsController : CQRSControllerFeatrueBased<ProductDto, Product, GetProductsQuery, DeleteProductCommand, CreateProductCommand>
{
    private readonly IProductService _productService;

    public ProductsController(
        ILocalizationService localizationService,
        ILogger<ProductsController> logger,
        IMediator mediator,
        AutoMapper.IMapper mapper,
        MapsterMapper.IMapper mapster,
        IProductService productService)
        : base(localizationService, logger, mediator, mapper, mapster)
    {
        _productService = productService;
    }

    /// <summary>
    /// Get all products with optional filtering
    /// </summary>
    /// <param name="query">Query parameters for filtering and pagination</param>
    /// <returns>List of products</returns>
    [HttpGet]
    public override ValueTask<HttpResponseWrapperList<ProductDto>> GetAllByQueryAsync([FromQuery] GetProductsQuery query)
    {
        return base.GetAllByQueryAsync(query);
    }

    /// <summary>
    /// Get a specific product by ID
    /// </summary>
    /// <param name="id">Product ID</param>
    /// <returns>Product details</returns>
    [HttpGet("{id:guid}")]
    public override ValueTask<HttpResponseWrapper<ProductDto?>> GetOneAsync(Guid id)
    {
        return TryOne<ProductDto?>(async (opt) =>
        {
            var product = await _productService.GetByGuId(id);
            if (product == null)
            {
                return opt.NotFound("Product not found");
            }
            return opt.Success(product);
        });
    }

    /// <summary>
    /// Create a new product
    /// </summary>
    /// <param name="command">Product creation data</param>
    /// <returns>Created product</returns>
    [HttpPost]
    public override ValueTask<HttpResponseWrapper<ProductDto?>> PostSaveAsync(CreateProductCommand command)
    {
        return base.PostSaveAsync(command);
    }

    /// <summary>
    /// Update an existing product
    /// </summary>
    /// <param name="id">Product ID</param>
    /// <param name="command">Product update data</param>
    /// <returns>Updated product</returns>
    [HttpPut("{id:guid}")]
    public ValueTask<HttpResponseWrapper<ProductDto?>> PutUpdateAsync(Guid id, UpdateProductCommand command)
    {
        return TryOne<ProductDto?>(async (opt) =>
        {
            command.Id = new(id);
            var dto = command.Adapt<ProductDto>();
            var updatedProduct = await _productService.Update(dto);
            
            if (updatedProduct == null)
            {
                return opt.NotFound("Product not found");
            }
            
            return opt.Success(updatedProduct);
        });
    }

    /// <summary>
    /// Delete a product
    /// </summary>
    /// <param name="id">Product ID</param>
    /// <returns>Deleted product</returns>
    [HttpDelete("{id:guid}")]
    public ValueTask<HttpResponseWrapper<ProductDto?>> DeleteAsync(Guid id)
    {
        var command = new DeleteProductCommand(id);
        return DeleteRemoveAsync(command);
    }

    /// <summary>
    /// Get products by category
    /// </summary>
    /// <param name="category">Category name</param>
    /// <returns>List of products in the category</returns>
    [HttpGet("category/{category}")]
    public ValueTask<HttpResponseWrapperList<ProductDto>> GetProductsByCategoryAsync(string category)
    {
        return TryMeny<ProductDto>(async (opt) =>
        {
            var products = await _productService.GetProductsByCategoryAsync(category);
            return opt.Success(products);
        });
    }

    /// <summary>
    /// Get products by price range
    /// </summary>
    /// <param name="minPrice">Minimum price</param>
    /// <param name="maxPrice">Maximum price</param>
    /// <returns>List of products in the price range</returns>
    [HttpGet("price-range")]
    public ValueTask<HttpResponseWrapperList<ProductDto>> GetProductsByPriceRangeAsync(
        [FromQuery] decimal minPrice, 
        [FromQuery] decimal maxPrice)
    {
        return TryMeny<ProductDto>(async (opt) =>
        {
            var products = await _productService.GetProductsByPriceRangeAsync(minPrice, maxPrice);
            return opt.Success(products);
        });
    }

    /// <summary>
    /// Get product by SKU
    /// </summary>
    /// <param name="sku">Product SKU</param>
    /// <returns>Product with the specified SKU</returns>
    [HttpGet("sku/{sku}")]
    public ValueTask<HttpResponseWrapper<ProductDto?>> GetProductBySkuAsync(string sku)
    {
        return TryOne<ProductDto?>(async (opt) =>
        {
            var product = await _productService.GetProductBySkuAsync(sku);
            if (product == null)
            {
                return opt.NotFound("Product not found");
            }
            return opt.Success(product);
        });
    }
}
