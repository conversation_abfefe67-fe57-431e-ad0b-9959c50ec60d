name: Update Version

on:
  push:
    tags: [ 'v*' ]
  workflow_dispatch:
    inputs:
      version:
        description: 'Version number (e.g., 1.2.3)'
        required: true
        type: string

jobs:
  update-version:
    runs-on: windows-latest
    defaults:
      run:
        working-directory: ./Prcsrly.Fly.Framework

    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      with:
        token: ${{ secrets.GITHUB_TOKEN }}

    - name: Extract version from tag
      id: extract_version
      run: |
        if ("${{ github.event_name }}" -eq "workflow_dispatch") {
          $VERSION = "${{ github.event.inputs.version }}"
        } else {
          $VERSION = "${{ github.ref }}" -replace "refs/tags/v", ""
        }
        echo "version=$VERSION" >> $env:GITHUB_OUTPUT
        Write-Host "Version: $VERSION"
      shell: pwsh

    - name: Update project versions
      run: |
        $VERSION = "${{ steps.extract_version.outputs.version }}"

        # Update Directory.Build.props
        (Get-Content "Directory.Build.props") -replace "<VersionPrefix>.*</VersionPrefix>", "<VersionPrefix>$VERSION</VersionPrefix>" | Set-Content "Directory.Build.props"

        # Update individual project files if they have explicit versions
        Get-ChildItem "src" -Recurse -Filter "*.csproj" | ForEach-Object {
          (Get-Content $_.FullName) -replace "<Version>.*</Version>", "<Version>$VERSION</Version>" | Set-Content $_.FullName
        }

        Write-Host "Updated version to $VERSION"
      shell: pwsh

    - name: Commit version changes
      if: github.event_name == 'workflow_dispatch'
      run: |
        git config --local user.email "<EMAIL>"
        git config --local user.name "GitHub Action"
        git add .
        if (git diff --staged --quiet) {
          Write-Host "No changes to commit"
        } else {
          git commit -m "Update version to ${{ steps.extract_version.outputs.version }}"
          git push
        }
      shell: pwsh
