using Prcsrly.Fly.Framework.Shared.Common
using Prcsrly.Fly.Framework.Shared.Common.Attributes
using Prcsrly.Fly.Framework.Shared.Common.Dtos
using System.Text;

namespace Prcsrly.Fly.Shared.Modules.Store.Dtos;

public class DtoDisbursementPermit : DtoBase
{
    [ClientInvisible]
    public Identifier CostCenterId { get; set; }
    public string CostCenterName { get; set; }
    [ClientInvisible]
    public Identifier StoreId { get; set; }
    [ClientInvisible]
    public DateTime? Date { get; set; }

    public long Number { get; set; }

    public string TimwFormated => Date?.ToString("HH:mm") ?? "";
    public string DateFormated => Date?.ToString("yyyy.MM.dd") ?? "";

    [ClientInvisible]
    public ICollection<DtoDisbursementPermitDetail> DisbursementPermitDetails { get; set; } = [];

    public string DetailsString
    {
        get
        {
            var def = "";


            if (DisbursementPermitDetails?.Any() == true)
            {
                var tt = new List<string>();

                foreach (var item in DisbursementPermitDetails)
                {
                    tt.Add($"[{item.Quantity} {item.UnitName} {item.ItemName}]");
                }
                def = string.Join(", ", tt);
            }


            return def;
        }
    }
}