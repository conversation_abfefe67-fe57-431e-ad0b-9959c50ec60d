using Prcsrly.Fly.Framework.Shared.Common;
using Prcsrly.Fly.Framework.Shared.Common.Requests;
using Prcsrly.Fly.Shared.Common;

namespace Prcsrly.Fly.Shared.Modules.Store.Requests;

public class CreateUpdateSupplyPermitRequest : CreateUpdateRequestBase
{
    public Identifier SupplierId { get; set; }
    public Identifier StoreId { get; set; }
    public DateTime Date { get; set; }

    public IEnumerable<CreateUpdateSupplyPermitDetailRequest>? SupplyPermitDetails { get; set; }
}