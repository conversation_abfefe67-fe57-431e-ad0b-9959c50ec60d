﻿<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>

        <!-- NuGet Package Properties -->
        <PackageId>Prcsrly.Fly.Shared</PackageId>
        <Version>1.0.0</Version>
        <Authors>Prcsrly</Authors>
        <Company>Prcsrly</Company>
        <Product>Fly Framework</Product>
        <Description>Application-specific shared components for Fly Framework including business domain modules, MVVM components for client applications, and shared DTOs and models.</Description>
        <PackageTags>framework;shared;business;mvvm;dto</PackageTags>
        <PackageProjectUrl>https://github.com/prcsrly/fly-framework</PackageProjectUrl>
        <RepositoryUrl>https://github.com/prcsrly/fly-framework</RepositoryUrl>
        <RepositoryType>git</RepositoryType>
        <PackageLicenseExpression>MIT</PackageLicenseExpression>
        <GeneratePackageOnBuild>true</GeneratePackageOnBuild>
        <IncludeSymbols>true</IncludeSymbols>
        <SymbolPackageFormat>snupkg</SymbolPackageFormat>

        <!-- Assembly Properties -->
        <AssemblyName>Prcsrly.Fly.Shared</AssemblyName>
        <RootNamespace>Prcsrly.Fly.Shared</RootNamespace>
    </PropertyGroup>

    <ItemGroup>
        <Folder Include="Modules\A0\Enums\" />
        <Folder Include="Modules\Factoring\Enums\" />
        <Folder Include="Modules\Factoring\Features\" />
        <Folder Include="Modules\Factoring\Requests\" />
        <Folder Include="Modules\Factoring\Response\" />
        <Folder Include="Modules\Factoring\SeededData\" />
        <Folder Include="Modules\Financial\Response\" />
        <Folder Include="Modules\Financial\SeededData\" />
        <Folder Include="Modules\Marketers\Enums\" />
        <Folder Include="Modules\Marketers\SeededData\" />
        <Folder Include="Modules\PurchaseAndSales\Response\" />
        <Folder Include="Modules\PurchaseAndSales\SeededData\" />
        <Folder Include="Modules\A0\Dtos\" />
        <Folder Include="Modules\A0\Features\" />
        <Folder Include="Modules\A0\SeededData\" />
        <Folder Include="Modules\A0\Response\" />
        <Folder Include="Modules\A0\Requests\" />
    </ItemGroup>

    <ItemGroup>
		<PackageReference Include="CommunityToolkit.Mvvm" Version="8.2.2" />
		<PackageReference Include="MediatR" Version="12.4.1" />
        <PackageReference Include="Microsoft.Extensions.Configuration.Abstractions" Version="8.0.0" />
        <PackageReference Include="Microsoft.Extensions.DependencyInjection.Abstractions" Version="8.0.1" />
		<PackageReference Include="FluentValidation.DependencyInjectionExtensions" Version="11.10.0" />
	</ItemGroup>

    <ItemGroup>
      <ProjectReference Include="..\Prcsrly.Fly.Framework.Shared\Prcsrly.Fly.Framework.Shared.csproj" />
    </ItemGroup>

</Project>
