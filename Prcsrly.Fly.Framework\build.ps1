#!/usr/bin/env pwsh

param(
    [string]$Configuration = "Release",
    [string]$VersionSuffix = "",
    [switch]$Pack = $false,
    [switch]$Clean = $false
)

$ErrorActionPreference = "Stop"

Write-Host "Building Prcsrly.Fly.Framework..." -ForegroundColor Green

# Clean if requested
if ($Clean) {
    Write-Host "Cleaning..." -ForegroundColor Yellow
    dotnet clean --configuration $Configuration
    if (Test-Path "artifacts") {
        Remove-Item -Recurse -Force "artifacts"
    }
}

# Restore packages
Write-Host "Restoring packages..." -ForegroundColor Yellow
dotnet restore

# Build
Write-Host "Building..." -ForegroundColor Yellow
$buildArgs = @(
    "build"
    "--configuration", $Configuration
    "--no-restore"
)

if ($VersionSuffix) {
    $buildArgs += "--version-suffix", $VersionSuffix
}

& dotnet @buildArgs

if ($LASTEXITCODE -ne 0) {
    Write-Error "Build failed"
    exit $LASTEXITCODE
}

# Pack if requested
if ($Pack) {
    Write-Host "Packing..." -ForegroundColor Yellow
    
    $packArgs = @(
        "pack"
        "--configuration", $Configuration
        "--no-build"
        "--output", "artifacts/packages"
    )
    
    if ($VersionSuffix) {
        $packArgs += "--version-suffix", $VersionSuffix
    }
    
    & dotnet @packArgs
    
    if ($LASTEXITCODE -ne 0) {
        Write-Error "Pack failed"
        exit $LASTEXITCODE
    }
    
    Write-Host "Packages created in artifacts/packages/" -ForegroundColor Green
    Get-ChildItem "artifacts/packages/*.nupkg" | ForEach-Object {
        Write-Host "  - $($_.Name)" -ForegroundColor Cyan
    }
}

Write-Host "Build completed successfully!" -ForegroundColor Green
