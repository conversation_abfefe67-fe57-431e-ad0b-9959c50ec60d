using Prcsrly.Fly.Framework.Shared.Common.Features
using Prcsrly.Fly.Framework.Shared.Common.Requests
using Prcsrly.Fly.Shared.Modules.Personnel.Dtos
using Prcsrly.Fly.Shared.Modules.Personnel.Requests;

namespace Prcsrly.Fly.Shared.Modules.Personnel.Features;

public interface IDepartmentFeatures : IFeatureBase<DtoDepartment, DepartmentQueryRequest, DeleteRequestBase, CreateUpdateNamedRequestBase>
{
}