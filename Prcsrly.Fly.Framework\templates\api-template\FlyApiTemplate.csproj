<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <RootNamespace>FlyApiTemplate</RootNamespace>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Prcsrly.Fly.Framework.Shared" Version="1.0.1" />
    <PackageReference Include="Prcsrly.Fly.Framework.Server" Version="1.0.1" />
    
    <!-- ASP.NET Core packages -->
    <PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="8.0.0" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="6.4.0" Condition="'$(UseSwagger)' == 'true'" />
    
    <!-- Entity Framework packages -->
    <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="8.0.0" Condition="'$(DatabaseProvider)' == 'SqlServer'" />
    <PackageReference Include="Pomelo.EntityFrameworkCore.MySql" Version="8.0.0" Condition="'$(DatabaseProvider)' == 'MySQL'" />
    <PackageReference Include="Npgsql.EntityFrameworkCore.PostgreSQL" Version="8.0.0" Condition="'$(DatabaseProvider)' == 'PostgreSQL'" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="8.0.0">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    
    <!-- MediatR for CQRS -->
    <PackageReference Include="MediatR" Version="12.2.0" />
    <PackageReference Include="MediatR.Extensions.Microsoft.DependencyInjection" Version="11.1.0" />
    
    <!-- AutoMapper -->
    <PackageReference Include="AutoMapper" Version="12.0.1" />
    <PackageReference Include="AutoMapper.Extensions.Microsoft.DependencyInjection" Version="12.0.1" />
    
    <!-- Mapster -->
    <PackageReference Include="Mapster" Version="7.4.0" />
    <PackageReference Include="Mapster.DependencyInjection" Version="1.0.1" />
    
    <!-- Authentication -->
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="8.0.0" Condition="'$(UseAuthentication)' == 'true'" />
    
    <!-- Logging -->
    <PackageReference Include="Serilog.AspNetCore" Version="8.0.0" />
    <PackageReference Include="Serilog.Sinks.Console" Version="5.0.0" />
    <PackageReference Include="Serilog.Sinks.File" Version="5.0.0" />
  </ItemGroup>

</Project>
