{"version": 2, "dgSpecHash": "M797c05gEgc=", "success": true, "projectFilePath": "C:\\Users\\<USER>\\source\\repos\\vscode\\prcsrly\\SmartiERP\\Prcsrly.Fly.Framework\\src\\Prcsrly.Fly.Framework.Shared\\Prcsrly.Fly.Framework.Shared.csproj", "expectedPackageFiles": ["C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration.abstractions\\8.0.0\\microsoft.extensions.configuration.abstractions.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.dependencyinjection.abstractions\\8.0.1\\microsoft.extensions.dependencyinjection.abstractions.8.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.primitives\\8.0.0\\microsoft.extensions.primitives.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\newtonsoft.json\\13.0.3\\newtonsoft.json.13.0.3.nupkg.sha512"], "logs": []}