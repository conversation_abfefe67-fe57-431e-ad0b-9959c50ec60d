using Prcsrly.Fly.Framework.Shared.Common
using Prcsrly.Fly.Framework.Shared.Common.Attributes
using Prcsrly.Fly.Framework.Shared.Common.Dtos
using System.Net.Sockets;

namespace Prcsrly.Fly.Shared.Modules.PurchaseAndSales.Dtos.Misc;

public class DtoBank : DtoBase
{
    public string Name { get; set; } = string.Empty;

    [ClientInvisible]
    public Identifier AccountId { get; set; }
    public long AccountNumber { get; set; }
    public string? Note { get; set; }
}

