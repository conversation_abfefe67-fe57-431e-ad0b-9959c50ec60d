using Prcsrly.Fly.Framework.Shared.Common.Dtos;

namespace Prcsrly.Fly.Shared.Modules.Global.Dtos;

public class DtoCompanyInfo : DtoBase
{
    public string? Name { get; set; }
    public string? NameEn { get; set; }

    public string? Type { get; set; }
    public string? TypeEn { get; set; }

    public string? Image { get; set; }
    
    public string? Address { get; set; }
    
    public string? PhoneNumber1 { get; set; }
    
    public string? PhoneNumber2 { get; set; }

    public string? PhoneNumber3 { get; set; }
    
    public string? Email { get; set; }
    
    public string? InvoiceNotice { get; set; }
}