using FlyApiTemplate.Data;
using FlyApiTemplate.Services;
using Microsoft.EntityFrameworkCore;
using Prcsrly.Fly.Framework.Server;
using Prcsrly.Fly.Framework.Server.Mapping;
using Serilog;
using System.Reflection;

var builder = WebApplication.CreateBuilder(args);

// Configure Serilog
Log.Logger = new LoggerConfiguration()
    .ReadFrom.Configuration(builder.Configuration)
    .Enrich.FromLogContext()
    .WriteTo.Console()
    .WriteTo.File("logs/log-.txt", rollingInterval: RollingInterval.Day)
    .CreateLogger();

builder.Host.UseSerilog();

// Add services to the container
builder.Services.AddControllers();

// Add Fly Framework services
builder.Services.InjectFrameworkServer(builder.Configuration);
builder.Services.InjectFrameworkMapping(builder.Configuration);

// Add Entity Framework
// Add Entity Framework with the selected database provider
var connectionString = builder.Configuration.GetConnectionString("DefaultConnection");

#if (DatabaseProvider == "SqlServer")
builder.Services.AddDbContext<ApplicationDbContext>(options =>
    options.UseSqlServer(connectionString));
#elif (DatabaseProvider == "MySQL")
builder.Services.AddDbContext<ApplicationDbContext>(options =>
    options.UseMySql(connectionString, ServerVersion.AutoDetect(connectionString)));
#elif (DatabaseProvider == "PostgreSQL")
builder.Services.AddDbContext<ApplicationDbContext>(options =>
    options.UseNpgsql(connectionString));
#endif

// Add MediatR
builder.Services.AddMediatR(cfg => cfg.RegisterServicesFromAssembly(Assembly.GetExecutingAssembly()));

// Add AutoMapper
builder.Services.AddAutoMapper(Assembly.GetExecutingAssembly());

// Add Mapster
builder.Services.AddMapster();

#if (UseSwagger)
// Add Swagger/OpenAPI
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen(c =>
{
    c.SwaggerDoc("v1", new() { Title = "FlyApiTemplate API", Version = "v1" });
    
#if (UseAuthentication)
    // Add JWT authentication to Swagger
    c.AddSecurityDefinition("Bearer", new()
    {
        Description = "JWT Authorization header using the Bearer scheme. Example: \"Authorization: Bearer {token}\"",
        Name = "Authorization",
        In = Microsoft.OpenApi.Models.ParameterLocation.Header,
        Type = Microsoft.OpenApi.Models.SecuritySchemeType.ApiKey,
        Scheme = "Bearer"
    });
    
    c.AddSecurityRequirement(new()
    {
        {
            new()
            {
                Reference = new() { Type = Microsoft.OpenApi.Models.ReferenceType.SecurityScheme, Id = "Bearer" }
            },
            Array.Empty<string>()
        }
    });
#endif
});
#endif

#if (IncludeSampleEntity)
// Register application services
builder.Services.AddScoped<IProductService, ProductService>();
#endif

// Add CORS
builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowAll", policy =>
    {
        policy.AllowAnyOrigin()
              .AllowAnyMethod()
              .AllowAnyHeader();
    });
});

var app = builder.Build();

// Configure the HTTP request pipeline
if (app.Environment.IsDevelopment())
{
#if (UseSwagger)
    app.UseSwagger();
    app.UseSwaggerUI(c =>
    {
        c.SwaggerEndpoint("/swagger/v1/swagger.json", "FlyApiTemplate API v1");
        c.RoutePrefix = string.Empty; // Set Swagger UI at the app's root
    });
#endif
}

#if (UseHttps)
app.UseHttpsRedirection();
#endif

app.UseCors("AllowAll");

// Use Fly Framework middleware
app.UseBaseFramework();

app.UseRouting();

#if (UseAuthentication)
app.UseAuthentication();
#endif
app.UseAuthorization();

app.MapControllers();

// Ensure database is created
using (var scope = app.Services.CreateScope())
{
    var context = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();
    context.Database.EnsureCreated();
}

try
{
    Log.Information("Starting FlyApiTemplate API");
    app.Run();
}
catch (Exception ex)
{
    Log.Fatal(ex, "Application terminated unexpectedly");
}
finally
{
    Log.CloseAndFlush();
}
