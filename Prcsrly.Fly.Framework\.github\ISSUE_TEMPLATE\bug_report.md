---
name: Bug report
about: Create a report to help us improve
title: '[BUG] '
labels: bug
assignees: ''
---

**Describe the bug**
A clear and concise description of what the bug is.

**To Reproduce**
Steps to reproduce the behavior:
1. Go to '...'
2. Click on '....'
3. Scroll down to '....'
4. See error

**Expected behavior**
A clear and concise description of what you expected to happen.

**Package Information**
- Package: [e.g., Prcsrly.Fly.Framework.Shared]
- Version: [e.g., 1.0.0]

**Environment**
- OS: [e.g., Windows 11, Ubuntu 20.04]
- .NET Version: [e.g., .NET 8.0]
- IDE: [e.g., Visual Studio 2022, VS Code]

**Additional context**
Add any other context about the problem here, including stack traces or error messages.
