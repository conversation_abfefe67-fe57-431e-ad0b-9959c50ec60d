using Prcsrly.Fly.Framework.Shared.Common;
using Prcsrly.Fly.Framework.Shared.Common.Requests;
using Prcsrly.Fly.Shared.Modules.PurchaseAndSales.Enums;

namespace Prcsrly.Fly.Shared.Modules.Store.Requests;

public class ItemQueryRequest : QueryRequestBase
{
    public Identifier? ItemId { get; set; }
}
public class ChangePriceRequest
{
    public Identifier ItemId { get; set; }
    public decimal Price { get; set; }
}
public class ExpireDateQueryRequest : QueryRequestBase
{
    public Identifier ItemId { get; set; }
    public Identifier StoreId { get; set; }
    public StorageType StorageType { get; set; }
    public Identifier? LinkedFile { get; set; }
}