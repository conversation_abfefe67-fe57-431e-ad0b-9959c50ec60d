﻿<?xml version="1.0" encoding="utf-8"?>
<package xmlns="http://schemas.microsoft.com/packaging/2012/06/nuspec.xsd">
  <metadata>
    <id>Prcsrly.Fly.Framework.Shared</id>
    <version>1.0.0</version>
    <authors>Prcsrly</authors>
    <license type="expression">MIT</license>
    <licenseUrl>https://licenses.nuget.org/MIT</licenseUrl>
    <projectUrl>https://github.com/prcsrly/fly-framework</projectUrl>
    <description>Core shared framework components for Fly Framework including common utilities, helpers, expression builders, and base exceptions.</description>
    <copyright>Copyright © Prcsrly 2025</copyright>
    <tags>framework shared utilities helpers</tags>
    <repository type="git" url="https://github.com/prcsrly/fly-framework" />
    <dependencies>
      <group targetFramework="net8.0">
        <dependency id="Microsoft.Extensions.Configuration.Abstractions" version="8.0.0" exclude="Build,Analyzers" />
        <dependency id="Microsoft.Extensions.DependencyInjection.Abstractions" version="8.0.1" exclude="Build,Analyzers" />
        <dependency id="Newtonsoft.Json" version="13.0.3" exclude="Build,Analyzers" />
      </group>
    </dependencies>
  </metadata>
  <files>
    <file src="C:\Users\<USER>\source\repos\vscode\prcsrly\SmartiERP\Prcsrly.Fly.Framework\src\Prcsrly.Fly.Framework.Shared\bin\Debug\net8.0\Prcsrly.Fly.Framework.Shared.pdb" target="lib\net8.0\Prcsrly.Fly.Framework.Shared.pdb" />
  </files>
</package>