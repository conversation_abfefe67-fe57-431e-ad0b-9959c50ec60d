using Prcsrly.Fly.Framework.Server.Persistence.Common;
using System.ComponentModel.DataAnnotations;

namespace FlyApiTemplate.Entities;

public class Product : TEntity
{
    [Required]
    [MaxLength(200)]
    public string Name { get; set; } = string.Empty;

    [MaxLength(1000)]
    public string Description { get; set; } = string.Empty;

    [Required]
    public decimal Price { get; set; }

    [MaxLength(50)]
    public string SKU { get; set; } = string.Empty;

    public int StockQuantity { get; set; }

    public bool IsActive { get; set; } = true;

    public DateTime? LastUpdated { get; set; }

    public string Category { get; set; } = string.Empty;

    public override string Display() => Name;
}
