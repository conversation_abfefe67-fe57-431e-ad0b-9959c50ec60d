# Fly Framework API Template

A comprehensive ASP.NET Core Web API project template built with the Prcsrly.Fly.Framework, designed to accelerate API development with best practices and modern patterns.

## 🚀 Features

- **🏗️ Fly Framework Integration** - Built on top of Prcsrly.Fly.Framework for rapid development
- **📋 CQRS Pattern** - Command Query Responsibility Segregation with MediatR
- **🗄️ Multi-Database Support** - SQL Server, MySQL, and PostgreSQL support
- **🔄 Object Mapping** - Both AutoMapper and Mapster integration
- **📚 API Documentation** - Swagger/OpenAPI with authentication support
- **📝 Structured Logging** - Serilog with file and console output
- **🔐 Authentication Ready** - JWT Bearer token authentication (optional)
- **📦 Sample CRUD Operations** - Complete Product entity with full CRUD operations
- **🎯 Clean Architecture** - Separation of concerns with proper layering
- **⚡ Performance Optimized** - Efficient data access patterns and caching

## 📋 Prerequisites

- .NET 8.0 SDK or later
- Database server (SQL Server, MySQL, or PostgreSQL)
- Visual Studio 2022, VS Code, or JetBrains Rider

## 🛠️ Installation

### Option 1: Install Template Globally

1. **Install the template:**
   ```bash
   # Windows
   .\install-template.ps1
   
   # Linux/Mac
   ./install-template.sh
   ```

2. **Create a new project:**
   ```bash
   dotnet new flyapi -n MyApiProject
   cd MyApiProject
   ```

### Option 2: Manual Installation

1. **Install from directory:**
   ```bash
   dotnet new install ./api-template
   ```

2. **Create project:**
   ```bash
   dotnet new flyapi -n MyApiProject
   ```

## 🎛️ Template Options

| Option | Type | Default | Description |
|--------|------|---------|-------------|
| `UseHttps` | bool | `true` | Enable HTTPS redirection |
| `UseSwagger` | bool | `true` | Include Swagger/OpenAPI documentation |
| `UseAuthentication` | bool | `true` | Include JWT authentication |
| `DatabaseProvider` | choice | `SqlServer` | Database provider (SqlServer/MySQL/PostgreSQL) |
| `IncludeSampleEntity` | bool | `true` | Include Product entity with CRUD operations |

## 📝 Usage Examples

### Basic API with SQL Server
```bash
dotnet new flyapi -n MyApi
```

### API with MySQL and no authentication
```bash
dotnet new flyapi -n MyApi --DatabaseProvider MySQL --UseAuthentication false
```

### Minimal API without sample entity
```bash
dotnet new flyapi -n MyApi --IncludeSampleEntity false --UseSwagger false
```

### PostgreSQL with all features
```bash
dotnet new flyapi -n MyApi --DatabaseProvider PostgreSQL --UseHttps true --UseAuthentication true
```

## 🏗️ Project Structure

```
MyApiProject/
├── Controllers/          # API Controllers with CQRS pattern
│   └── ProductsController.cs
├── Data/                # Database Context and configurations
│   └── ApplicationDbContext.cs
├── Dtos/                # Data Transfer Objects
│   └── ProductDto.cs
├── Entities/            # Domain Entities
│   └── Product.cs
├── Handlers/            # MediatR Request Handlers
│   └── ProductHandlers.cs
├── Mapping/             # AutoMapper & Mapster configurations
│   ├── ProductMappingProfile.cs
│   └── MapsterConfig.cs
├── Requests/            # CQRS Commands and Queries
│   └── ProductRequests.cs
├── Services/            # Business Logic Services
│   ├── IProductService.cs
│   └── ProductService.cs
├── Properties/
│   └── launchSettings.json
├── Program.cs           # Application entry point
├── appsettings.json     # Configuration
└── README.md           # Project documentation
```

## 🔧 Configuration

### Database Connection

Update the connection string in `appsettings.json`:

```json
{
  "ConnectionStrings": {
    "DefaultConnection": "your-connection-string-here"
  }
}
```

**Connection String Examples:**

- **SQL Server:** `Server=(localdb)\\mssqllocaldb;Database=MyApiDb;Trusted_Connection=true;`
- **MySQL:** `Server=localhost;Database=MyApiDb;Uid=root;Pwd=password;`
- **PostgreSQL:** `Host=localhost;Database=MyApiDb;Username=postgres;Password=password`

### JWT Authentication (if enabled)

Configure JWT settings in `appsettings.json`:

```json
{
  "Authentication": {
    "JwtSettings": {
      "SecretKey": "your-super-secret-key-at-least-32-characters",
      "Issuer": "MyApi",
      "Audience": "MyApi-Users",
      "ExpirationInMinutes": 60
    }
  }
}
```

## 🚀 Getting Started

1. **Update connection string** in `appsettings.json`
2. **Run the application:**
   ```bash
   dotnet run
   ```
3. **Access the API:**
   - Swagger UI: `https://localhost:5001`
   - API Base: `https://localhost:5001/api`

## 📚 API Endpoints (Sample Product Entity)

| Method | Endpoint | Description |
|--------|----------|-------------|
| GET | `/api/products` | Get all products with filtering |
| GET | `/api/products/{id}` | Get product by ID |
| POST | `/api/products` | Create new product |
| PUT | `/api/products/{id}` | Update product |
| DELETE | `/api/products/{id}` | Delete product |
| GET | `/api/products/category/{category}` | Get products by category |
| GET | `/api/products/price-range` | Get products by price range |
| GET | `/api/products/sku/{sku}` | Get product by SKU |

## 🔄 Development Workflow

### Adding New Entities

1. **Create Entity** in `Entities/`
2. **Create DTO** in `Dtos/`
3. **Create Requests** in `Requests/`
4. **Create Service** interface and implementation in `Services/`
5. **Create Handlers** in `Handlers/`
6. **Create Controller** in `Controllers/`
7. **Add Mappings** in `Mapping/`
8. **Update DbContext** to include new entity

### Database Migrations

```bash
# Add migration
dotnet ef migrations add InitialCreate

# Update database
dotnet ef database update

# Remove last migration
dotnet ef migrations remove
```

## 🧪 Testing

The template includes structured logging and error handling. To test:

1. **Run the application**
2. **Open Swagger UI** at `https://localhost:5001`
3. **Test the Product endpoints**
4. **Check logs** in the `logs/` directory

## 🔧 Customization

### Adding Custom Middleware

Add to `Program.cs`:
```csharp
app.UseMiddleware<YourCustomMiddleware>();
```

### Adding Custom Services

Register in `Program.cs`:
```csharp
builder.Services.AddScoped<IYourService, YourService>();
```

### Custom Validation

Add validation attributes to DTOs and requests, or implement custom validators.

## 📦 Dependencies

- **Prcsrly.Fly.Framework.Shared** - Core framework components
- **Prcsrly.Fly.Framework.Server** - Server-side framework features
- **Entity Framework Core** - Database access
- **MediatR** - CQRS pattern implementation
- **AutoMapper & Mapster** - Object mapping
- **Serilog** - Structured logging
- **Swagger/OpenAPI** - API documentation

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

This template is licensed under the MIT License.

## 🆘 Support

For issues and questions:
- Check the [Fly Framework documentation](https://github.com/prcsrly/Fly-Framework)
- Create an issue in the repository
- Review the sample code in the template

---

**Happy coding with Fly Framework! 🚀**
