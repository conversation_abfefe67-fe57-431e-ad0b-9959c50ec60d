using FlyApiTemplate.Dtos;
using FlyApiTemplate.Requests;
using FlyApiTemplate.Services;
using MediatR;
using Prcsrly.Fly.Framework.Shared.Common.Http;

namespace FlyApiTemplate.Handlers;

// Query Handler
public class GetProductsQueryHandler : IRequestHandler<GetProductsQuery, HttpResponseWrapperList<ProductDto>>
{
    private readonly IProductService _productService;
    private readonly ILogger<GetProductsQueryHandler> _logger;

    public GetProductsQueryHandler(IProductService productService, ILogger<GetProductsQueryHandler> logger)
    {
        _productService = productService;
        _logger = logger;
    }

    public async Task<HttpResponseWrapperList<ProductDto>> Handle(GetProductsQuery request, CancellationToken cancellationToken)
    {
        try
        {
            // Build filter expression
            var products = await _productService.GetAllAsync(p => 
                (string.IsNullOrEmpty(request.Category) || p.Category == request.Category) &&
                (!request.MinPrice.HasValue || p.Price >= request.MinPrice.Value) &&
                (!request.MaxPrice.HasValue || p.Price <= request.MaxPrice.Value) &&
                (!request.IsActive.HasValue || p.IsActive == request.IsActive.Value) &&
                (string.IsNullOrEmpty(request.SearchText) || 
                 p.Name.Contains(request.SearchText) || 
                 p.Description.Contains(request.SearchText) ||
                 p.SKU.Contains(request.SearchText)),
                request.Page,
                request.Liimit);

            return new HttpResponseWrapperList<ProductDto>
            {
                Data = products,
                Status = true,
                StatusCode = 200,
                CurrentPage = request.Page ?? 1,
                TotalCount = products.Count()
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while getting products");
            return new HttpResponseWrapperList<ProductDto>
            {
                Status = false,
                StatusCode = 500,
                ErrorMessage = "An error occurred while retrieving products"
            };
        }
    }
}

// Create Handler
public class CreateProductCommandHandler : IRequestHandler<CreateProductCommand, HttpResponseWrapper<ProductDto?>>
{
    private readonly IProductService _productService;
    private readonly ILogger<CreateProductCommandHandler> _logger;

    public CreateProductCommandHandler(IProductService productService, ILogger<CreateProductCommandHandler> logger)
    {
        _productService = productService;
        _logger = logger;
    }

    public async Task<HttpResponseWrapper<ProductDto?>> Handle(CreateProductCommand request, CancellationToken cancellationToken)
    {
        try
        {
            var productDto = new ProductDto
            {
                Name = request.Name,
                Description = request.Description,
                Price = request.Price,
                SKU = request.SKU,
                StockQuantity = request.StockQuantity,
                IsActive = request.IsActive,
                Category = request.Category
            };

            var createdProduct = await _productService.Add(productDto);

            if (createdProduct == null)
            {
                return new HttpResponseWrapper<ProductDto?>
                {
                    Status = false,
                    StatusCode = 400,
                    ErrorMessage = "Failed to create product"
                };
            }

            return new HttpResponseWrapper<ProductDto?>
            {
                Data = createdProduct,
                Status = true,
                StatusCode = 201
            };
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "Validation error while creating product");
            return new HttpResponseWrapper<ProductDto?>
            {
                Status = false,
                StatusCode = 400,
                ErrorMessage = ex.Message
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while creating product");
            return new HttpResponseWrapper<ProductDto?>
            {
                Status = false,
                StatusCode = 500,
                ErrorMessage = "An error occurred while creating the product"
            };
        }
    }
}

// Delete Handler
public class DeleteProductCommandHandler : IRequestHandler<DeleteProductCommand, HttpResponseWrapper<ProductDto>>
{
    private readonly IProductService _productService;
    private readonly ILogger<DeleteProductCommandHandler> _logger;

    public DeleteProductCommandHandler(IProductService productService, ILogger<DeleteProductCommandHandler> logger)
    {
        _productService = productService;
        _logger = logger;
    }

    public async Task<HttpResponseWrapper<ProductDto>> Handle(DeleteProductCommand request, CancellationToken cancellationToken)
    {
        try
        {
            var product = await _productService.GetByGuId(request.Id.Value);
            if (product == null)
            {
                return new HttpResponseWrapper<ProductDto>
                {
                    Status = false,
                    StatusCode = 404,
                    ErrorMessage = "Product not found"
                };
            }

            var deletedProduct = await _productService.Remove(product);

            return new HttpResponseWrapper<ProductDto>
            {
                Data = deletedProduct,
                Status = true,
                StatusCode = 200
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while deleting product with ID {ProductId}", request.Id);
            return new HttpResponseWrapper<ProductDto>
            {
                Status = false,
                StatusCode = 500,
                ErrorMessage = "An error occurred while deleting the product"
            };
        }
    }
}
