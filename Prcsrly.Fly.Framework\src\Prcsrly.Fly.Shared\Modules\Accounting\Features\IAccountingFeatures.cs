using Prcsrly.Fly.Framework.Shared.Common.Collections;
using Prcsrly.Fly.Framework.Shared.Common.Features;
using Prcsrly.Fly.Framework.Shared.Common.Http;
using Prcsrly.Fly.Framework.Shared.Common.Requests;
using Prcsrly.Fly.Framework.Shared.Common.ValueTypes;
using Prcsrly.Fly.Shared.Modules.Accounting.Dtos;
using Prcsrly.Fly.Shared.Modules.Accounting.Requests;

namespace Prcsrly.Fly.Shared.Modules.Accounting.Features;

public interface IAccountingFeatures
//  : IFeatureBase<DtoAccount>
    : IFeatureBase<DtoAccount, AccountQueryRequest, DeleteRequestBase, CreateAccountRequest>
{
    ValueTask<HttpResponseWrapperList<DtoAccount>> GetAccountTreeAsync(Guid parentId);
    ValueTask<HttpResponseWrapperList<DefaultLookup>> GetLookupAccounts(AccountQueryRequest request);
    ValueTask<HttpResponseWrapperList<DefaultLookup>> GetLookupCurrencies(CurrencyQueryRequest request);
    ValueTask<HttpResponseWrapper<GetAccountBalanceResponse>> GetAccountBalanc(GetAccountBalance request);
}