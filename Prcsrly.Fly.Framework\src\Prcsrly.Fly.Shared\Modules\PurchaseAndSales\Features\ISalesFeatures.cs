using Prcsrly.Fly.Framework.Shared.Common.Features;
using Prcsrly.Fly.Framework.Shared.Common.Http;
using Prcsrly.Fly.Framework.Shared.Common.ValueTypes;
using Prcsrly.Fly.Shared.Modules.Marketers.Requests;
using Prcsrly.Fly.Shared.Modules.Personnel.Dtos;
using Prcsrly.Fly.Shared.Modules.Personnel.Requests;
using Prcsrly.Fly.Shared.Modules.PurchaseAndQuantityCrediting.Requests;
using Prcsrly.Fly.Shared.Modules.PurchaseAndSales.Dtos;
using Prcsrly.Fly.Shared.Modules.PurchaseAndSales.Dtos.Sell;
using Prcsrly.Fly.Shared.Modules.PurchaseAndSales.Requests;
using Prcsrly.Fly.Shared.Modules.Store.Requests;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Prcsrly.Fly.Shared.Modules.PurchaseAndSales.Features;

public interface IFastSalesFeatures : IFeatureBase<DtoSalesInvoice, SalesInvoiceQueryRequest, DeleteSalesInvoiceRequest, FastSaleInvoiceCreateRequest>
{

}
public interface ISalesFeatures : IFeatureBase<DtoSalesInvoice, SalesInvoiceQueryRequest, DeleteSalesInvoiceRequest, SalesInvoiceCreateRequest>
{
    ValueTask<HttpResponseWrapperList<DefaultLookup>> GetLookupItems(ItemQueryRequest request);
    ValueTask<HttpResponseWrapperList<DefaultLookup>> GetLookupUnits(UnitQueryRequest request);
    ValueTask<HttpResponseWrapperList<DefaultLookup>> GetLookupStores(StoreQueryRequest request);
    ValueTask<HttpResponseWrapperList<DefaultLookup>> GetLookupClients(ClientQueryRequest request);
    ValueTask<HttpResponseWrapperList<DefaultLookup>> GetLookupSafes(SafeQueryRequest request);
    ValueTask<HttpResponseWrapper<ItemInfoQueryResponse>> PostItemInfoQuery(GetItemInfoQueryRequest queryRequest);
    ValueTask<HttpResponseWrapper<long>> GetNextNumber();
    ValueTask<HttpResponseWrapperList<DefaultLookup>> GetLookupRepresentatives(MarketerQueryRequest request);
}
public interface IQuantityCreditingFeatures : IFeatureBase<DtoQuantityCreditingInvoice, QuantityCreditingInvoiceQueryRequest, DeleteQuantityCreditingInvoiceRequest, QuantityCreditingInvoiceCreateRequest>
{
    ValueTask<HttpResponseWrapper<long>> GetNextNumber();
}
