<Project>
  <PropertyGroup>
    <!-- Common properties for all projects -->
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <LangVersion>latest</LangVersion>
    
    <!-- Common NuGet Package Properties -->
    <Authors>Prcsrly</Authors>
    <Company>Prcsrly</Company>
    <Product>Fly Framework</Product>
    <Copyright>Copyright © Prcsrly 2025</Copyright>
    <PackageProjectUrl>https://github.com/prcsrly/fly-framework</PackageProjectUrl>
    <RepositoryUrl>https://github.com/prcsrly/fly-framework</RepositoryUrl>
    <RepositoryType>git</RepositoryType>
    <PackageLicenseExpression>MIT</PackageLicenseExpression>
    <GeneratePackageOnBuild>true</GeneratePackageOnBuild>
    <IncludeSymbols>true</IncludeSymbols>
    <SymbolPackageFormat>snupkg</SymbolPackageFormat>
    <PackageRequireLicenseAcceptance>false</PackageRequireLicenseAcceptance>
    
    <!-- Version information -->
    <VersionPrefix>1.0.0</VersionPrefix>
    <VersionSuffix Condition="'$(Configuration)' == 'Debug'">preview</VersionSuffix>
    
    <!-- Build properties -->
    <TreatWarningsAsErrors>false</TreatWarningsAsErrors>
    <WarningsAsErrors />
    <WarningsNotAsErrors />
    
    <!-- Documentation -->
    <GenerateDocumentationFile>true</GenerateDocumentationFile>
    <NoWarn>$(NoWarn);CS1591</NoWarn> <!-- Missing XML comment for publicly visible type or member -->
  </PropertyGroup>
  
  <!-- Package output directory -->
  <PropertyGroup>
    <PackageOutputPath>$(MSBuildThisFileDirectory)artifacts\packages</PackageOutputPath>
  </PropertyGroup>
</Project>
