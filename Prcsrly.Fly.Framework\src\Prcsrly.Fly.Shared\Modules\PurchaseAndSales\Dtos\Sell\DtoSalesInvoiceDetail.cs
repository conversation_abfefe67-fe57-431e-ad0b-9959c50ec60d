using Prcsrly.Fly.Framework.Shared.Common;
using Prcsrly.Fly.Framework.Shared.Common.Attributes;
using Prcsrly.Fly.Framework.Shared.Common.Dtos;
using Prcsrly.Fly.Framework.Shared.Extentions;
using Prcsrly.Fly.Shared.Modules.Accounting.Dtos;
using Prcsrly.Fly.Shared.Modules.PurchaseAndSales.Enums;

namespace Prcsrly.Fly.Shared.Modules.PurchaseAndSales.Dtos.Sell;

public class DtoSalesInvoiceDetail : DtoBase
{
    [ClientInvisible]
    public Identifier SalesInvoiceId { get; set; }

    [ClientInvisible]
    public Identifier UnitId { get; set; }
    public string UnitName { get; set; } = string.Empty;

    [ClientInvisible]
    public Identifier ItemId { get; set; }
    public string ItemName { get; set; } = string.Empty;
    public string ItemBarcode { get; set; } = string.Empty;

    public decimal Quantity { get; set; }
    public decimal Price { get; set; }

    public decimal Discount { get; set; }

    [ClientInvisible]
    public DateTime? ExpireDate { get; set; }
    public string? ExpireDateString => ExpireDate?.ToDottedDate();

    public decimal TotalAmount => Price * Quantity - Discount;

    public decimal CountingPrince(int mode)
    {
        var realPrice = Math.Round(Price * Quantity, 3);
        decimal final = realPrice;
        if (mode == (int)DiscountType.Percent)
        {
            final = realPrice - (realPrice * (Discount / 100));
        }
        else if (mode == (int)DiscountType.Static)
        {
            final = realPrice - Discount;
        }
        return Math.Round(final, 3);
    }
}