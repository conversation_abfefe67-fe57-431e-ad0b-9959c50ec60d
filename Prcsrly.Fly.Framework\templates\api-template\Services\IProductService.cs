using FlyApiTemplate.Dtos;
using FlyApiTemplate.Entities;
using Prcsrly.Fly.Framework.Server.Persistence;

namespace FlyApiTemplate.Services;

public interface IProductService : IServiceBase<ProductDto, Product>
{
    // Add any additional service methods specific to Product here
    ValueTask<IEnumerable<ProductDto>> GetProductsByCategoryAsync(string category);
    ValueTask<IEnumerable<ProductDto>> GetProductsByPriceRangeAsync(decimal minPrice, decimal maxPrice);
    ValueTask<ProductDto?> GetProductBySkuAsync(string sku);
    ValueTask<bool> IsSkuUniqueAsync(string sku, Guid? excludeProductId = null);
}
