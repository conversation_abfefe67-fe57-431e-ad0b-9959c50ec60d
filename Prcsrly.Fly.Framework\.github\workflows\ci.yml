name: Continuous Integration

on:
  pull_request:
    branches: [ main, master ]
  workflow_dispatch:

env:
  DOTNET_VERSION: '8.0.x'
  CONFIGURATION: Release

jobs:
  build-and-test:
    runs-on: windows-latest
    defaults:
      run:
        working-directory: ./Prcsrly.Fly.Framework

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup .NET
      uses: actions/setup-dotnet@v4
      with:
        dotnet-version: ${{ env.DOTNET_VERSION }}

    - name: Cache NuGet packages
      uses: actions/cache@v4
      with:
        path: ~/.nuget/packages
        key: ${{ runner.os }}-nuget-${{ hashFiles('**/Prcsrly.Fly.Framework/**/*.csproj') }}
        restore-keys: |
          ${{ runner.os }}-nuget-

    - name: Restore dependencies
      run: dotnet restore

    - name: Build solution
      run: dotnet build --configuration ${{ env.CONFIGURATION }} --no-restore

    - name: Run tests
      run: dotnet test --configuration ${{ env.CONFIGURATION }} --no-build --verbosity normal --collect:"XPlat Code Coverage"
      continue-on-error: true

    - name: Pack NuGet packages (validation)
      run: |
        # Pack each project individually to ensure all packages are created
        dotnet pack "src/Prcsrly.Fly.Framework.Shared/Prcsrly.Fly.Framework.Shared.csproj" --configuration ${{ env.CONFIGURATION }} --no-build --output ./artifacts/packages
        dotnet pack "src/Prcsrly.Fly.Framework.Server/Prcsrly.Fly.Framework.Server.csproj" --configuration ${{ env.CONFIGURATION }} --no-build --output ./artifacts/packages

    - name: Validate package contents
      run: |
        Write-Host "Validating NuGet packages..."
        Get-ChildItem "./artifacts/packages/*.nupkg" | ForEach-Object {
          Write-Host "Package: $($_.Name)"
          dotnet nuget verify $_.FullName
          if ($LASTEXITCODE -ne 0) { Write-Host "Warning: Package verification failed for $($_.Name)" }
        }
      shell: pwsh

    - name: Upload packages for review
      uses: actions/upload-artifact@v4
      with:
        name: pr-packages
        path: ./Prcsrly.Fly.Framework/artifacts/packages/*.nupkg
        retention-days: 7
