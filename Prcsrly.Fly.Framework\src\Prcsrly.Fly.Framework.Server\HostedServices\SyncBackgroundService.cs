using Prcsrly.Fly.Framework.Server.Common.Sync;
using Prcsrly.Fly.Framework.Server.Settings;
using Prcsrly.Fly.Framework.Shared.Features;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Prcsrly.Fly.Framework.Server.HostedServices;

public class SyncBackgroundService(
    IServiceProvider services,
    ILogger<SyncBackgroundService> logger) : BackgroundService
{
    private readonly TimeSpan _interval = TimeSpan.FromSeconds(30);
    private readonly ILogger<SyncBackgroundService> _logger = logger;
    private readonly IServiceProvider ScopedServiceProvider = services.CreateScope().ServiceProvider;

    private ISyncService? _syncService;
    private SyncSettings? _synctSettings;

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {

        _synctSettings = ScopedServiceProvider.GetRequiredService<IOptions<SyncSettings>>().Value;
        if (!_synctSettings.Enabled)
        {
            _logger.LogInformation("Sync is not enabled!");
            return;
        }

        _syncService = ScopedServiceProvider.GetRequiredService<ISyncService>();

        _logger.LogInformation("Sync Background Service is starting");
        while (!stoppingToken.IsCancellationRequested)
        {
            try
            {
                await _syncService.UploadPendingSyncsAsync(stoppingToken);
                await _syncService.DownloadSyncsAsync(stoppingToken);
            }
            catch (Exception ex) when (ex is not OperationCanceledException)
            {
                _logger.LogError(ex, "Error occurred in sync background service");
            }

            await Task.Delay(_interval, stoppingToken);
        }

        _logger.LogInformation("Sync Background Service is stopping");
    }
}