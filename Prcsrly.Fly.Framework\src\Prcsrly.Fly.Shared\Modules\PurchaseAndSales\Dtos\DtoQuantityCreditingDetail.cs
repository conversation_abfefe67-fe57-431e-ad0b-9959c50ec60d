using Prcsrly.Fly.Framework.Shared.Common
using Prcsrly.Fly.Framework.Shared.Common.Attributes
using Prcsrly.Fly.Framework.Shared.Common.Dtos
using Prcsrly.Fly.Shared.Modules.Accounting.Dtos;

namespace Prcsrly.Fly.Shared.Modules.PurchaseAndSales.Dtos;

public class DtoQuantityCreditingInvoiceDetail : DtoBase
{
    [ClientInvisible]
    public Identifier QuantityCreditingInvoiceId { get; set; }

    [ClientInvisible]
    public Identifier UnitId { get; set; }
    public string UnitName { get; set; } = string.Empty;

    [ClientInvisible]
    public Identifier ItemId { get; set; }
    public string ItemName { get; set; } = string.Empty;
    public string ItemBarcode { get; set; } = string.Empty;

    public decimal Quantity { get; set; }
    public decimal Price { get; set; }
    public decimal Discount { get; set; }
    public DateTime? ExpireDate { get; set; }

    [ClientInvisible]
    public decimal TotalAmount => Price * Quantity;
}